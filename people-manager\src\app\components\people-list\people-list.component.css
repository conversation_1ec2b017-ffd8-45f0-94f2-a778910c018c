.people-list-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.actions-bar {
  margin-bottom: 20px;
  display: flex;
  justify-content: flex-end;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px;
}

.loading-container p {
  margin-top: 16px;
  color: #666;
}

.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px;
  color: #666;
}

.no-data mat-icon {
  font-size: 48px;
  width: 48px;
  height: 48px;
  margin-bottom: 16px;
}

.table-container {
  overflow-x: auto;
}

.people-table {
  width: 100%;
  margin-top: 16px;
}

.people-table th {
  font-weight: 600;
  color: #333;
}

.people-table td {
  padding: 12px 8px;
}

.people-table tr:hover {
  background-color: #f5f5f5;
}

.mat-column-actions {
  width: 120px;
  text-align: center;
}

.mat-icon-button {
  margin: 0 4px;
}

@media (max-width: 768px) {
  .people-list-container {
    padding: 10px;
  }

  .actions-bar {
    justify-content: center;
  }

  .people-table {
    font-size: 14px;
  }
}