{"code": "(window.webpackJsonp=window.webpackJsonp||[]).push([[2],{2:function(module,exports,__webpack_require__){module.exports=__webpack_require__(\"hN/g\")},\"hN/g\":function(module,__webpack_exports__,__webpack_require__){\"use strict\";__webpack_require__.r(__webpack_exports__),__webpack_require__(\"pDpN\")},pDpN:function(module,exports){!function(global){const performance=global.performance;function mark(name){performance&&performance.mark&&performance.mark(name)}function performanceMeasure(name,label){performance&&performance.measure&&performance.measure(name,label)}mark(\"Zone\");const checkDuplicate=!0===global.__zone_symbol__forceDuplicateZoneCheck;if(global.Zone){if(checkDuplicate||\"function\"!=typeof global.Zone.__symbol__)throw new Error(\"Zone already loaded.\");return global.Zone}class Zone{constructor(parent,zoneSpec){this._parent=parent,this._name=zoneSpec?zoneSpec.name||\"unnamed\":\"<root>\",this._properties=zoneSpec&&zoneSpec.properties||{},this._zoneDelegate=new ZoneDelegate(this,this._parent&&this._parent._zoneDelegate,zoneSpec)}static assertZonePatched(){if(global.Promise!==patches.ZoneAwarePromise)throw new Error(\"Zone.js has detected that ZoneAwarePromise `(window|global).Promise` has been overwritten.\\nMost likely cause is that a Promise polyfill has been loaded after Zone.js (Polyfilling Promise api is not necessary when zone.js is loaded. If you must load one, do so before loading zone.js.)\")}static get root(){let zone=Zone.current;for(;zone.parent;)zone=zone.parent;return zone}static get current(){return _currentZoneFrame.zone}static get currentTask(){return _currentTask}static __load_patch(name,fn){if(patches.hasOwnProperty(name)){if(checkDuplicate)throw Error(\"Already loaded patch: \"+name)}else if(!global[\"__Zone_disable_\"+name]){const perfName=\"Zone:\"+name;mark(perfName),patches[name]=fn(global,Zone,_api),performanceMeasure(perfName,perfName)}}get parent(){return this._parent}get name(){return this._name}get(key){const zone=this.getZoneWith(key);if(zone)return zone._properties[key]}getZoneWith(key){let current=this;for(;current;){if(current._properties.hasOwnProperty(key))return current;current=current._parent}return null}fork(zoneSpec){if(!zoneSpec)throw new Error(\"ZoneSpec required!\");return this._zoneDelegate.fork(this,zoneSpec)}wrap(callback,source){if(\"function\"!=typeof callback)throw new Error(\"Expecting function got: \"+callback);const _callback=this._zoneDelegate.intercept(this,callback,source),zone=this;return function(){return zone.runGuarded(_callback,this,arguments,source)}}run(callback,applyThis,applyArgs,source){_currentZoneFrame={parent:_currentZoneFrame,zone:this};try{return this._zoneDelegate.invoke(this,callback,applyThis,applyArgs,source)}finally{_currentZoneFrame=_currentZoneFrame.parent}}runGuarded(callback,applyThis=null,applyArgs,source){_currentZoneFrame={parent:_currentZoneFrame,zone:this};try{try{return this._zoneDelegate.invoke(this,callback,applyThis,applyArgs,source)}catch(error){if(this._zoneDelegate.handleError(this,error))throw error}}finally{_currentZoneFrame=_currentZoneFrame.parent}}runTask(task,applyThis,applyArgs){if(task.zone!=this)throw new Error(\"A task can only be run in the zone of creation! (Creation: \"+(task.zone||NO_ZONE).name+\"; Execution: \"+this.name+\")\");if(task.state===notScheduled&&(task.type===eventTask||task.type===macroTask))return;const reEntryGuard=task.state!=running;reEntryGuard&&task._transitionTo(running,scheduled),task.runCount++;const previousTask=_currentTask;_currentTask=task,_currentZoneFrame={parent:_currentZoneFrame,zone:this};try{task.type==macroTask&&task.data&&!task.data.isPeriodic&&(task.cancelFn=void 0);try{return this._zoneDelegate.invokeTask(this,task,applyThis,applyArgs)}catch(error){if(this._zoneDelegate.handleError(this,error))throw error}}finally{task.state!==notScheduled&&task.state!==unknown&&(task.type==eventTask||task.data&&task.data.isPeriodic?reEntryGuard&&task._transitionTo(scheduled,running):(task.runCount=0,this._updateTaskCount(task,-1),reEntryGuard&&task._transitionTo(notScheduled,running,notScheduled))),_currentZoneFrame=_currentZoneFrame.parent,_currentTask=previousTask}}scheduleTask(task){if(task.zone&&task.zone!==this){let newZone=this;for(;newZone;){if(newZone===task.zone)throw Error(`can not reschedule task to ${this.name} which is descendants of the original zone ${task.zone.name}`);newZone=newZone.parent}}task._transitionTo(scheduling,notScheduled);const zoneDelegates=[];task._zoneDelegates=zoneDelegates,task._zone=this;try{task=this._zoneDelegate.scheduleTask(this,task)}catch(err){throw task._transitionTo(unknown,scheduling,notScheduled),this._zoneDelegate.handleError(this,err),err}return task._zoneDelegates===zoneDelegates&&this._updateTaskCount(task,1),task.state==scheduling&&task._transitionTo(scheduled,scheduling),task}scheduleMicroTask(source,callback,data,customSchedule){return this.scheduleTask(new ZoneTask(microTask,source,callback,data,customSchedule,void 0))}scheduleMacroTask(source,callback,data,customSchedule,customCancel){return this.scheduleTask(new ZoneTask(macroTask,source,callback,data,customSchedule,customCancel))}scheduleEventTask(source,callback,data,customSchedule,customCancel){return this.scheduleTask(new ZoneTask(eventTask,source,callback,data,customSchedule,customCancel))}cancelTask(task){if(task.zone!=this)throw new Error(\"A task can only be cancelled in the zone of creation! (Creation: \"+(task.zone||NO_ZONE).name+\"; Execution: \"+this.name+\")\");task._transitionTo(canceling,scheduled,running);try{this._zoneDelegate.cancelTask(this,task)}catch(err){throw task._transitionTo(unknown,canceling),this._zoneDelegate.handleError(this,err),err}return this._updateTaskCount(task,-1),task._transitionTo(notScheduled,canceling),task.runCount=0,task}_updateTaskCount(task,count){const zoneDelegates=task._zoneDelegates;-1==count&&(task._zoneDelegates=null);for(let i=0;i<zoneDelegates.length;i++)zoneDelegates[i]._updateTaskCount(task.type,count)}}Zone.__symbol__=__symbol__;const DELEGATE_ZS={name:\"\",onHasTask:(delegate,_,target,hasTaskState)=>delegate.hasTask(target,hasTaskState),onScheduleTask:(delegate,_,target,task)=>delegate.scheduleTask(target,task),onInvokeTask:(delegate,_,target,task,applyThis,applyArgs)=>delegate.invokeTask(target,task,applyThis,applyArgs),onCancelTask:(delegate,_,target,task)=>delegate.cancelTask(target,task)};class ZoneDelegate{constructor(zone,parentDelegate,zoneSpec){this._taskCounts={microTask:0,macroTask:0,eventTask:0},this.zone=zone,this._parentDelegate=parentDelegate,this._forkZS=zoneSpec&&(zoneSpec&&zoneSpec.onFork?zoneSpec:parentDelegate._forkZS),this._forkDlgt=zoneSpec&&(zoneSpec.onFork?parentDelegate:parentDelegate._forkDlgt),this._forkCurrZone=zoneSpec&&(zoneSpec.onFork?this.zone:parentDelegate.zone),this._interceptZS=zoneSpec&&(zoneSpec.onIntercept?zoneSpec:parentDelegate._interceptZS),this._interceptDlgt=zoneSpec&&(zoneSpec.onIntercept?parentDelegate:parentDelegate._interceptDlgt),this._interceptCurrZone=zoneSpec&&(zoneSpec.onIntercept?this.zone:parentDelegate.zone),this._invokeZS=zoneSpec&&(zoneSpec.onInvoke?zoneSpec:parentDelegate._invokeZS),this._invokeDlgt=zoneSpec&&(zoneSpec.onInvoke?parentDelegate:parentDelegate._invokeDlgt),this._invokeCurrZone=zoneSpec&&(zoneSpec.onInvoke?this.zone:parentDelegate.zone),this._handleErrorZS=zoneSpec&&(zoneSpec.onHandleError?zoneSpec:parentDelegate._handleErrorZS),this._handleErrorDlgt=zoneSpec&&(zoneSpec.onHandleError?parentDelegate:parentDelegate._handleErrorDlgt),this._handleErrorCurrZone=zoneSpec&&(zoneSpec.onHandleError?this.zone:parentDelegate.zone),this._scheduleTaskZS=zoneSpec&&(zoneSpec.onScheduleTask?zoneSpec:parentDelegate._scheduleTaskZS),this._scheduleTaskDlgt=zoneSpec&&(zoneSpec.onScheduleTask?parentDelegate:parentDelegate._scheduleTaskDlgt),this._scheduleTaskCurrZone=zoneSpec&&(zoneSpec.onScheduleTask?this.zone:parentDelegate.zone),this._invokeTaskZS=zoneSpec&&(zoneSpec.onInvokeTask?zoneSpec:parentDelegate._invokeTaskZS),this._invokeTaskDlgt=zoneSpec&&(zoneSpec.onInvokeTask?parentDelegate:parentDelegate._invokeTaskDlgt),this._invokeTaskCurrZone=zoneSpec&&(zoneSpec.onInvokeTask?this.zone:parentDelegate.zone),this._cancelTaskZS=zoneSpec&&(zoneSpec.onCancelTask?zoneSpec:parentDelegate._cancelTaskZS),this._cancelTaskDlgt=zoneSpec&&(zoneSpec.onCancelTask?parentDelegate:parentDelegate._cancelTaskDlgt),this._cancelTaskCurrZone=zoneSpec&&(zoneSpec.onCancelTask?this.zone:parentDelegate.zone),this._hasTaskZS=null,this._hasTaskDlgt=null,this._hasTaskDlgtOwner=null,this._hasTaskCurrZone=null;const zoneSpecHasTask=zoneSpec&&zoneSpec.onHasTask;(zoneSpecHasTask||parentDelegate&&parentDelegate._hasTaskZS)&&(this._hasTaskZS=zoneSpecHasTask?zoneSpec:DELEGATE_ZS,this._hasTaskDlgt=parentDelegate,this._hasTaskDlgtOwner=this,this._hasTaskCurrZone=zone,zoneSpec.onScheduleTask||(this._scheduleTaskZS=DELEGATE_ZS,this._scheduleTaskDlgt=parentDelegate,this._scheduleTaskCurrZone=this.zone),zoneSpec.onInvokeTask||(this._invokeTaskZS=DELEGATE_ZS,this._invokeTaskDlgt=parentDelegate,this._invokeTaskCurrZone=this.zone),zoneSpec.onCancelTask||(this._cancelTaskZS=DELEGATE_ZS,this._cancelTaskDlgt=parentDelegate,this._cancelTaskCurrZone=this.zone))}fork(targetZone,zoneSpec){return this._forkZS?this._forkZS.onFork(this._forkDlgt,this.zone,targetZone,zoneSpec):new Zone(targetZone,zoneSpec)}intercept(targetZone,callback,source){return this._interceptZS?this._interceptZS.onIntercept(this._interceptDlgt,this._interceptCurrZone,targetZone,callback,source):callback}invoke(targetZone,callback,applyThis,applyArgs,source){return this._invokeZS?this._invokeZS.onInvoke(this._invokeDlgt,this._invokeCurrZone,targetZone,callback,applyThis,applyArgs,source):callback.apply(applyThis,applyArgs)}handleError(targetZone,error){return!this._handleErrorZS||this._handleErrorZS.onHandleError(this._handleErrorDlgt,this._handleErrorCurrZone,targetZone,error)}scheduleTask(targetZone,task){let returnTask=task;if(this._scheduleTaskZS)this._hasTaskZS&&returnTask._zoneDelegates.push(this._hasTaskDlgtOwner),returnTask=this._scheduleTaskZS.onScheduleTask(this._scheduleTaskDlgt,this._scheduleTaskCurrZone,targetZone,task),returnTask||(returnTask=task);else if(task.scheduleFn)task.scheduleFn(task);else{if(task.type!=microTask)throw new Error(\"Task is missing scheduleFn.\");scheduleMicroTask(task)}return returnTask}invokeTask(targetZone,task,applyThis,applyArgs){return this._invokeTaskZS?this._invokeTaskZS.onInvokeTask(this._invokeTaskDlgt,this._invokeTaskCurrZone,targetZone,task,applyThis,applyArgs):task.callback.apply(applyThis,applyArgs)}cancelTask(targetZone,task){let value;if(this._cancelTaskZS)value=this._cancelTaskZS.onCancelTask(this._cancelTaskDlgt,this._cancelTaskCurrZone,targetZone,task);else{if(!task.cancelFn)throw Error(\"Task is not cancelable\");value=task.cancelFn(task)}return value}hasTask(targetZone,isEmpty){try{this._hasTaskZS&&this._hasTaskZS.onHasTask(this._hasTaskDlgt,this._hasTaskCurrZone,targetZone,isEmpty)}catch(err){this.handleError(targetZone,err)}}_updateTaskCount(type,count){const counts=this._taskCounts,prev=counts[type],next=counts[type]=prev+count;if(next<0)throw new Error(\"More tasks executed then were scheduled.\");0!=prev&&0!=next||this.hasTask(this.zone,{microTask:counts.microTask>0,macroTask:counts.macroTask>0,eventTask:counts.eventTask>0,change:type})}}class ZoneTask{constructor(type,source,callback,options,scheduleFn,cancelFn){this._zone=null,this.runCount=0,this._zoneDelegates=null,this._state=\"notScheduled\",this.type=type,this.source=source,this.data=options,this.scheduleFn=scheduleFn,this.cancelFn=cancelFn,this.callback=callback;const self=this;this.invoke=type===eventTask&&options&&options.useG?ZoneTask.invokeTask:function(){return ZoneTask.invokeTask.call(global,self,this,arguments)}}static invokeTask(task,target,args){task||(task=this),_numberOfNestedTaskFrames++;try{return task.runCount++,task.zone.runTask(task,target,args)}finally{1==_numberOfNestedTaskFrames&&drainMicroTaskQueue(),_numberOfNestedTaskFrames--}}get zone(){return this._zone}get state(){return this._state}cancelScheduleRequest(){this._transitionTo(notScheduled,scheduling)}_transitionTo(toState,fromState1,fromState2){if(this._state!==fromState1&&this._state!==fromState2)throw new Error(`${this.type} '${this.source}': can not transition to '${toState}', expecting state '${fromState1}'${fromState2?\" or '\"+fromState2+\"'\":\"\"}, was '${this._state}'.`);this._state=toState,toState==notScheduled&&(this._zoneDelegates=null)}toString(){return this.data&&void 0!==this.data.handleId?this.data.handleId.toString():Object.prototype.toString.call(this)}toJSON(){return{type:this.type,state:this.state,source:this.source,zone:this.zone.name,runCount:this.runCount}}}const symbolSetTimeout=__symbol__(\"setTimeout\"),symbolPromise=__symbol__(\"Promise\"),symbolThen=__symbol__(\"then\");let nativeMicroTaskQueuePromise,_microTaskQueue=[],_isDrainingMicrotaskQueue=!1;function scheduleMicroTask(task){if(0===_numberOfNestedTaskFrames&&0===_microTaskQueue.length)if(nativeMicroTaskQueuePromise||global[symbolPromise]&&(nativeMicroTaskQueuePromise=global[symbolPromise].resolve(0)),nativeMicroTaskQueuePromise){let nativeThen=nativeMicroTaskQueuePromise[symbolThen];nativeThen||(nativeThen=nativeMicroTaskQueuePromise.then),nativeThen.call(nativeMicroTaskQueuePromise,drainMicroTaskQueue)}else global[symbolSetTimeout](drainMicroTaskQueue,0);task&&_microTaskQueue.push(task)}function drainMicroTaskQueue(){if(!_isDrainingMicrotaskQueue){for(_isDrainingMicrotaskQueue=!0;_microTaskQueue.length;){const queue=_microTaskQueue;_microTaskQueue=[];for(let i=0;i<queue.length;i++){const task=queue[i];try{task.zone.runTask(task,null,null)}catch(error){_api.onUnhandledError(error)}}}_api.microtaskDrainDone(),_isDrainingMicrotaskQueue=!1}}const NO_ZONE={name:\"NO ZONE\"},notScheduled=\"notScheduled\",scheduling=\"scheduling\",scheduled=\"scheduled\",running=\"running\",canceling=\"canceling\",unknown=\"unknown\",microTask=\"microTask\",macroTask=\"macroTask\",eventTask=\"eventTask\",patches={},_api={symbol:__symbol__,currentZoneFrame:()=>_currentZoneFrame,onUnhandledError:noop,microtaskDrainDone:noop,scheduleMicroTask:scheduleMicroTask,showUncaughtError:()=>!Zone[__symbol__(\"ignoreConsoleErrorUncaughtError\")],patchEventTarget:()=>[],patchOnProperties:noop,patchMethod:()=>noop,bindArguments:()=>[],patchThen:()=>noop,patchMacroTask:()=>noop,setNativePromise:NativePromise=>{NativePromise&&\"function\"==typeof NativePromise.resolve&&(nativeMicroTaskQueuePromise=NativePromise.resolve(0))},patchEventPrototype:()=>noop,isIEOrEdge:()=>!1,getGlobalObjects:()=>{},ObjectDefineProperty:()=>noop,ObjectGetOwnPropertyDescriptor:()=>{},ObjectCreate:()=>{},ArraySlice:()=>[],patchClass:()=>noop,wrapWithCurrentZone:()=>noop,filterProperties:()=>[],attachOriginToPatched:()=>noop,_redefineProperty:()=>noop,patchCallbacks:()=>noop};let _currentZoneFrame={parent:null,zone:new Zone(null,null)},_currentTask=null,_numberOfNestedTaskFrames=0;function noop(){}function __symbol__(name){return\"__zone_symbol__\"+name}performanceMeasure(\"Zone\",\"Zone\"),global.Zone=Zone}(\"undefined\"!=typeof window&&window||\"undefined\"!=typeof self&&self||global),Zone.__load_patch(\"ZoneAwarePromise\",(global,Zone,api)=>{const ObjectGetOwnPropertyDescriptor=Object.getOwnPropertyDescriptor,ObjectDefineProperty=Object.defineProperty,__symbol__=api.symbol,_uncaughtPromiseErrors=[],symbolPromise=__symbol__(\"Promise\"),symbolThen=__symbol__(\"then\");api.onUnhandledError=e=>{if(api.showUncaughtError()){const rejection=e&&e.rejection;rejection?console.error(\"Unhandled Promise rejection:\",rejection instanceof Error?rejection.message:rejection,\"; Zone:\",e.zone.name,\"; Task:\",e.task&&e.task.source,\"; Value:\",rejection,rejection instanceof Error?rejection.stack:void 0):console.error(e)}},api.microtaskDrainDone=()=>{for(;_uncaughtPromiseErrors.length;)for(;_uncaughtPromiseErrors.length;){const uncaughtPromiseError=_uncaughtPromiseErrors.shift();try{uncaughtPromiseError.zone.runGuarded(()=>{throw uncaughtPromiseError})}catch(error){handleUnhandledRejection(error)}}};const UNHANDLED_PROMISE_REJECTION_HANDLER_SYMBOL=__symbol__(\"unhandledPromiseRejectionHandler\");function handleUnhandledRejection(e){api.onUnhandledError(e);try{const handler=Zone[UNHANDLED_PROMISE_REJECTION_HANDLER_SYMBOL];handler&&\"function\"==typeof handler&&handler.call(this,e)}catch(err){}}function isThenable(value){return value&&value.then}function forwardResolution(value){return value}function forwardRejection(rejection){return ZoneAwarePromise.reject(rejection)}const symbolState=__symbol__(\"state\"),symbolValue=__symbol__(\"value\"),symbolFinally=__symbol__(\"finally\"),symbolParentPromiseValue=__symbol__(\"parentPromiseValue\"),symbolParentPromiseState=__symbol__(\"parentPromiseState\");function makeResolver(promise,state){return v=>{try{resolvePromise(promise,state,v)}catch(err){resolvePromise(promise,!1,err)}}}const CURRENT_TASK_TRACE_SYMBOL=__symbol__(\"currentTaskTrace\");function resolvePromise(promise,state,value){const onceWrapper=function(){let wasCalled=!1;return function(wrappedFunction){return function(){wasCalled||(wasCalled=!0,wrappedFunction.apply(null,arguments))}}}();if(promise===value)throw new TypeError(\"Promise resolved with itself\");if(null===promise[symbolState]){let then=null;try{\"object\"!=typeof value&&\"function\"!=typeof value||(then=value&&value.then)}catch(err){return onceWrapper(()=>{resolvePromise(promise,!1,err)})(),promise}if(!1!==state&&value instanceof ZoneAwarePromise&&value.hasOwnProperty(symbolState)&&value.hasOwnProperty(symbolValue)&&null!==value[symbolState])clearRejectedNoCatch(value),resolvePromise(promise,value[symbolState],value[symbolValue]);else if(!1!==state&&\"function\"==typeof then)try{then.call(value,onceWrapper(makeResolver(promise,state)),onceWrapper(makeResolver(promise,!1)))}catch(err){onceWrapper(()=>{resolvePromise(promise,!1,err)})()}else{promise[symbolState]=state;const queue=promise[symbolValue];if(promise[symbolValue]=value,promise[symbolFinally]===symbolFinally&&!0===state&&(promise[symbolState]=promise[symbolParentPromiseState],promise[symbolValue]=promise[symbolParentPromiseValue]),!1===state&&value instanceof Error){const trace=Zone.currentTask&&Zone.currentTask.data&&Zone.currentTask.data.__creationTrace__;trace&&ObjectDefineProperty(value,CURRENT_TASK_TRACE_SYMBOL,{configurable:!0,enumerable:!1,writable:!0,value:trace})}for(let i=0;i<queue.length;)scheduleResolveOrReject(promise,queue[i++],queue[i++],queue[i++],queue[i++]);if(0==queue.length&&0==state){promise[symbolState]=0;try{throw new Error(\"Uncaught (in promise): \"+((obj=value)&&obj.toString===Object.prototype.toString?(obj.constructor&&obj.constructor.name||\"\")+\": \"+JSON.stringify(obj):obj?obj.toString():Object.prototype.toString.call(obj))+(value&&value.stack?\"\\n\"+value.stack:\"\"))}catch(err){const error=err;error.rejection=value,error.promise=promise,error.zone=Zone.current,error.task=Zone.currentTask,_uncaughtPromiseErrors.push(error),api.scheduleMicroTask()}}}}var obj;return promise}const REJECTION_HANDLED_HANDLER=__symbol__(\"rejectionHandledHandler\");function clearRejectedNoCatch(promise){if(0===promise[symbolState]){try{const handler=Zone[REJECTION_HANDLED_HANDLER];handler&&\"function\"==typeof handler&&handler.call(this,{rejection:promise[symbolValue],promise:promise})}catch(err){}promise[symbolState]=!1;for(let i=0;i<_uncaughtPromiseErrors.length;i++)promise===_uncaughtPromiseErrors[i].promise&&_uncaughtPromiseErrors.splice(i,1)}}function scheduleResolveOrReject(promise,zone,chainPromise,onFulfilled,onRejected){clearRejectedNoCatch(promise);const promiseState=promise[symbolState],delegate=promiseState?\"function\"==typeof onFulfilled?onFulfilled:forwardResolution:\"function\"==typeof onRejected?onRejected:forwardRejection;zone.scheduleMicroTask(\"Promise.then\",()=>{try{const parentPromiseValue=promise[symbolValue],isFinallyPromise=chainPromise&&symbolFinally===chainPromise[symbolFinally];isFinallyPromise&&(chainPromise[symbolParentPromiseValue]=parentPromiseValue,chainPromise[symbolParentPromiseState]=promiseState);const value=zone.run(delegate,void 0,isFinallyPromise&&delegate!==forwardRejection&&delegate!==forwardResolution?[]:[parentPromiseValue]);resolvePromise(chainPromise,!0,value)}catch(error){resolvePromise(chainPromise,!1,error)}},chainPromise)}class ZoneAwarePromise{constructor(executor){const promise=this;if(!(promise instanceof ZoneAwarePromise))throw new Error(\"Must be an instanceof Promise.\");promise[symbolState]=null,promise[symbolValue]=[];try{executor&&executor(makeResolver(promise,!0),makeResolver(promise,!1))}catch(error){resolvePromise(promise,!1,error)}}static toString(){return\"function ZoneAwarePromise() { [native code] }\"}static resolve(value){return resolvePromise(new this(null),!0,value)}static reject(error){return resolvePromise(new this(null),!1,error)}static race(values){let resolve,reject,promise=new this((res,rej)=>{resolve=res,reject=rej});function onResolve(value){resolve(value)}function onReject(error){reject(error)}for(let value of values)isThenable(value)||(value=this.resolve(value)),value.then(onResolve,onReject);return promise}static all(values){let resolve,reject,promise=new this((res,rej)=>{resolve=res,reject=rej}),unresolvedCount=2,valueIndex=0;const resolvedValues=[];for(let value of values){isThenable(value)||(value=this.resolve(value));const curValueIndex=valueIndex;value.then(value=>{resolvedValues[curValueIndex]=value,unresolvedCount--,0===unresolvedCount&&resolve(resolvedValues)},reject),unresolvedCount++,valueIndex++}return unresolvedCount-=2,0===unresolvedCount&&resolve(resolvedValues),promise}get[Symbol.toStringTag](){return\"Promise\"}then(onFulfilled,onRejected){const chainPromise=new this.constructor(null),zone=Zone.current;return null==this[symbolState]?this[symbolValue].push(zone,chainPromise,onFulfilled,onRejected):scheduleResolveOrReject(this,zone,chainPromise,onFulfilled,onRejected),chainPromise}catch(onRejected){return this.then(null,onRejected)}finally(onFinally){const chainPromise=new this.constructor(null);chainPromise[symbolFinally]=symbolFinally;const zone=Zone.current;return null==this[symbolState]?this[symbolValue].push(zone,chainPromise,onFinally,onFinally):scheduleResolveOrReject(this,zone,chainPromise,onFinally,onFinally),chainPromise}}ZoneAwarePromise.resolve=ZoneAwarePromise.resolve,ZoneAwarePromise.reject=ZoneAwarePromise.reject,ZoneAwarePromise.race=ZoneAwarePromise.race,ZoneAwarePromise.all=ZoneAwarePromise.all;const NativePromise=global[symbolPromise]=global.Promise,ZONE_AWARE_PROMISE=Zone.__symbol__(\"ZoneAwarePromise\");let desc=ObjectGetOwnPropertyDescriptor(global,\"Promise\");desc&&!desc.configurable||(desc&&delete desc.writable,desc&&delete desc.value,desc||(desc={configurable:!0,enumerable:!0}),desc.get=function(){return global[ZONE_AWARE_PROMISE]?global[ZONE_AWARE_PROMISE]:global[symbolPromise]},desc.set=function(NewNativePromise){NewNativePromise===ZoneAwarePromise?global[ZONE_AWARE_PROMISE]=NewNativePromise:(global[symbolPromise]=NewNativePromise,NewNativePromise.prototype[symbolThen]||patchThen(NewNativePromise),api.setNativePromise(NewNativePromise))},ObjectDefineProperty(global,\"Promise\",desc)),global.Promise=ZoneAwarePromise;const symbolThenPatched=__symbol__(\"thenPatched\");function patchThen(Ctor){const proto=Ctor.prototype,prop=ObjectGetOwnPropertyDescriptor(proto,\"then\");if(prop&&(!1===prop.writable||!prop.configurable))return;const originalThen=proto.then;proto[symbolThen]=originalThen,Ctor.prototype.then=function(onResolve,onReject){return new ZoneAwarePromise((resolve,reject)=>{originalThen.call(this,resolve,reject)}).then(onResolve,onReject)},Ctor[symbolThenPatched]=!0}if(api.patchThen=patchThen,NativePromise){patchThen(NativePromise);const fetch=global.fetch;\"function\"==typeof fetch&&(global[api.symbol(\"fetch\")]=fetch,global.fetch=(fn=fetch,function(){let resultPromise=fn.apply(this,arguments);if(resultPromise instanceof ZoneAwarePromise)return resultPromise;let ctor=resultPromise.constructor;return ctor[symbolThenPatched]||patchThen(ctor),resultPromise}))}var fn;return Promise[Zone.__symbol__(\"uncaughtPromiseErrors\")]=_uncaughtPromiseErrors,ZoneAwarePromise});const ObjectGetOwnPropertyDescriptor=Object.getOwnPropertyDescriptor,ObjectDefineProperty=Object.defineProperty,ObjectGetPrototypeOf=Object.getPrototypeOf,ObjectCreate=Object.create,ArraySlice=Array.prototype.slice,ZONE_SYMBOL_ADD_EVENT_LISTENER=Zone.__symbol__(\"addEventListener\"),ZONE_SYMBOL_REMOVE_EVENT_LISTENER=Zone.__symbol__(\"removeEventListener\");function wrapWithCurrentZone(callback,source){return Zone.current.wrap(callback,source)}function scheduleMacroTaskWithCurrentZone(source,callback,data,customSchedule,customCancel){return Zone.current.scheduleMacroTask(source,callback,data,customSchedule,customCancel)}const zoneSymbol=Zone.__symbol__,isWindowExists=\"undefined\"!=typeof window,internalWindow=isWindowExists?window:void 0,_global=isWindowExists&&internalWindow||\"object\"==typeof self&&self||global,NULL_ON_PROP_VALUE=[null];function bindArguments(args,source){for(let i=args.length-1;i>=0;i--)\"function\"==typeof args[i]&&(args[i]=wrapWithCurrentZone(args[i],source+\"_\"+i));return args}function isPropertyWritable(propertyDesc){return!propertyDesc||!1!==propertyDesc.writable&&!(\"function\"==typeof propertyDesc.get&&void 0===propertyDesc.set)}const isWebWorker=\"undefined\"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope,isNode=!(\"nw\"in _global)&&void 0!==_global.process&&\"[object process]\"==={}.toString.call(_global.process),isBrowser=!isNode&&!isWebWorker&&!(!isWindowExists||!internalWindow.HTMLElement),isMix=void 0!==_global.process&&\"[object process]\"==={}.toString.call(_global.process)&&!isWebWorker&&!(!isWindowExists||!internalWindow.HTMLElement),zoneSymbolEventNames={},wrapFn=function(event){if(!(event=event||_global.event))return;let eventNameSymbol=zoneSymbolEventNames[event.type];eventNameSymbol||(eventNameSymbol=zoneSymbolEventNames[event.type]=zoneSymbol(\"ON_PROPERTY\"+event.type));const target=this||event.target||_global,listener=target[eventNameSymbol];let result;if(isBrowser&&target===internalWindow&&\"error\"===event.type){const errorEvent=event;result=listener&&listener.call(this,errorEvent.message,errorEvent.filename,errorEvent.lineno,errorEvent.colno,errorEvent.error),!0===result&&event.preventDefault()}else result=listener&&listener.apply(this,arguments),null==result||result||event.preventDefault();return result};function patchProperty(obj,prop,prototype){let desc=ObjectGetOwnPropertyDescriptor(obj,prop);if(!desc&&prototype&&ObjectGetOwnPropertyDescriptor(prototype,prop)&&(desc={enumerable:!0,configurable:!0}),!desc||!desc.configurable)return;const onPropPatchedSymbol=zoneSymbol(\"on\"+prop+\"patched\");if(obj.hasOwnProperty(onPropPatchedSymbol)&&obj[onPropPatchedSymbol])return;delete desc.writable,delete desc.value;const originalDescGet=desc.get,originalDescSet=desc.set,eventName=prop.substr(2);let eventNameSymbol=zoneSymbolEventNames[eventName];eventNameSymbol||(eventNameSymbol=zoneSymbolEventNames[eventName]=zoneSymbol(\"ON_PROPERTY\"+eventName)),desc.set=function(newValue){let target=this;target||obj!==_global||(target=_global),target&&(target[eventNameSymbol]&&target.removeEventListener(eventName,wrapFn),originalDescSet&&originalDescSet.apply(target,NULL_ON_PROP_VALUE),\"function\"==typeof newValue?(target[eventNameSymbol]=newValue,target.addEventListener(eventName,wrapFn,!1)):target[eventNameSymbol]=null)},desc.get=function(){let target=this;if(target||obj!==_global||(target=_global),!target)return null;const listener=target[eventNameSymbol];if(listener)return listener;if(originalDescGet){let value=originalDescGet&&originalDescGet.call(this);if(value)return desc.set.call(this,value),\"function\"==typeof target.removeAttribute&&target.removeAttribute(prop),value}return null},ObjectDefineProperty(obj,prop,desc),obj[onPropPatchedSymbol]=!0}function patchOnProperties(obj,properties,prototype){if(properties)for(let i=0;i<properties.length;i++)patchProperty(obj,\"on\"+properties[i],prototype);else{const onProperties=[];for(const prop in obj)\"on\"==prop.substr(0,2)&&onProperties.push(prop);for(let j=0;j<onProperties.length;j++)patchProperty(obj,onProperties[j],prototype)}}const originalInstanceKey=zoneSymbol(\"originalInstance\");function patchClass(className){const OriginalClass=_global[className];if(!OriginalClass)return;_global[zoneSymbol(className)]=OriginalClass,_global[className]=function(){const a=bindArguments(arguments,className);switch(a.length){case 0:this[originalInstanceKey]=new OriginalClass;break;case 1:this[originalInstanceKey]=new OriginalClass(a[0]);break;case 2:this[originalInstanceKey]=new OriginalClass(a[0],a[1]);break;case 3:this[originalInstanceKey]=new OriginalClass(a[0],a[1],a[2]);break;case 4:this[originalInstanceKey]=new OriginalClass(a[0],a[1],a[2],a[3]);break;default:throw new Error(\"Arg list too long.\")}},attachOriginToPatched(_global[className],OriginalClass);const instance=new OriginalClass((function(){}));let prop;for(prop in instance)\"XMLHttpRequest\"===className&&\"responseBlob\"===prop||function(prop){\"function\"==typeof instance[prop]?_global[className].prototype[prop]=function(){return this[originalInstanceKey][prop].apply(this[originalInstanceKey],arguments)}:ObjectDefineProperty(_global[className].prototype,prop,{set:function(fn){\"function\"==typeof fn?(this[originalInstanceKey][prop]=wrapWithCurrentZone(fn,className+\".\"+prop),attachOriginToPatched(this[originalInstanceKey][prop],fn)):this[originalInstanceKey][prop]=fn},get:function(){return this[originalInstanceKey][prop]}})}(prop);for(prop in OriginalClass)\"prototype\"!==prop&&OriginalClass.hasOwnProperty(prop)&&(_global[className][prop]=OriginalClass[prop])}function patchMethod(target,name,patchFn){let proto=target;for(;proto&&!proto.hasOwnProperty(name);)proto=ObjectGetPrototypeOf(proto);!proto&&target[name]&&(proto=target);const delegateName=zoneSymbol(name);let delegate=null;if(proto&&!(delegate=proto[delegateName])&&(delegate=proto[delegateName]=proto[name],isPropertyWritable(proto&&ObjectGetOwnPropertyDescriptor(proto,name)))){const patchDelegate=patchFn(delegate,delegateName,name);proto[name]=function(){return patchDelegate(this,arguments)},attachOriginToPatched(proto[name],delegate)}return delegate}function patchMacroTask(obj,funcName,metaCreator){let setNative=null;function scheduleTask(task){const data=task.data;return data.args[data.cbIdx]=function(){task.invoke.apply(this,arguments)},setNative.apply(data.target,data.args),task}setNative=patchMethod(obj,funcName,delegate=>function(self,args){const meta=metaCreator(self,args);return meta.cbIdx>=0&&\"function\"==typeof args[meta.cbIdx]?scheduleMacroTaskWithCurrentZone(meta.name,args[meta.cbIdx],meta,scheduleTask):delegate.apply(self,args)})}function attachOriginToPatched(patched,original){patched[zoneSymbol(\"OriginalDelegate\")]=original}let isDetectedIEOrEdge=!1,ieOrEdge=!1;function isIE(){try{const ua=internalWindow.navigator.userAgent;if(-1!==ua.indexOf(\"MSIE \")||-1!==ua.indexOf(\"Trident/\"))return!0}catch(error){}return!1}function isIEOrEdge(){if(isDetectedIEOrEdge)return ieOrEdge;isDetectedIEOrEdge=!0;try{const ua=internalWindow.navigator.userAgent;-1===ua.indexOf(\"MSIE \")&&-1===ua.indexOf(\"Trident/\")&&-1===ua.indexOf(\"Edge/\")||(ieOrEdge=!0)}catch(error){}return ieOrEdge}Zone.__load_patch(\"toString\",global=>{const originalFunctionToString=Function.prototype.toString,ORIGINAL_DELEGATE_SYMBOL=zoneSymbol(\"OriginalDelegate\"),PROMISE_SYMBOL=zoneSymbol(\"Promise\"),ERROR_SYMBOL=zoneSymbol(\"Error\"),newFunctionToString=function(){if(\"function\"==typeof this){const originalDelegate=this[ORIGINAL_DELEGATE_SYMBOL];if(originalDelegate)return\"function\"==typeof originalDelegate?originalFunctionToString.call(originalDelegate):Object.prototype.toString.call(originalDelegate);if(this===Promise){const nativePromise=global[PROMISE_SYMBOL];if(nativePromise)return originalFunctionToString.call(nativePromise)}if(this===Error){const nativeError=global[ERROR_SYMBOL];if(nativeError)return originalFunctionToString.call(nativeError)}}return originalFunctionToString.call(this)};newFunctionToString[ORIGINAL_DELEGATE_SYMBOL]=originalFunctionToString,Function.prototype.toString=newFunctionToString;const originalObjectToString=Object.prototype.toString;Object.prototype.toString=function(){return this instanceof Promise?\"[object Promise]\":originalObjectToString.call(this)}});let passiveSupported=!1;if(\"undefined\"!=typeof window)try{const options=Object.defineProperty({},\"passive\",{get:function(){passiveSupported=!0}});window.addEventListener(\"test\",options,options),window.removeEventListener(\"test\",options,options)}catch(err){passiveSupported=!1}const OPTIMIZED_ZONE_EVENT_TASK_DATA={useG:!0},zoneSymbolEventNames$1={},globalSources={},EVENT_NAME_SYMBOL_REGX=/^__zone_symbol__(\\w+)(true|false)$/;function patchEventTarget(_global,apis,patchOptions){const ADD_EVENT_LISTENER=patchOptions&&patchOptions.add||\"addEventListener\",REMOVE_EVENT_LISTENER=patchOptions&&patchOptions.rm||\"removeEventListener\",LISTENERS_EVENT_LISTENER=patchOptions&&patchOptions.listeners||\"eventListeners\",REMOVE_ALL_LISTENERS_EVENT_LISTENER=patchOptions&&patchOptions.rmAll||\"removeAllListeners\",zoneSymbolAddEventListener=zoneSymbol(ADD_EVENT_LISTENER),ADD_EVENT_LISTENER_SOURCE=\".\"+ADD_EVENT_LISTENER+\":\",invokeTask=function(task,target,event){if(task.isRemoved)return;const delegate=task.callback;\"object\"==typeof delegate&&delegate.handleEvent&&(task.callback=event=>delegate.handleEvent(event),task.originalDelegate=delegate),task.invoke(task,target,[event]);const options=task.options;options&&\"object\"==typeof options&&options.once&&target[REMOVE_EVENT_LISTENER].call(target,event.type,task.originalDelegate?task.originalDelegate:task.callback,options)},globalZoneAwareCallback=function(event){if(!(event=event||_global.event))return;const target=this||event.target||_global,tasks=target[zoneSymbolEventNames$1[event.type].false];if(tasks)if(1===tasks.length)invokeTask(tasks[0],target,event);else{const copyTasks=tasks.slice();for(let i=0;i<copyTasks.length&&(!event||!0!==event.__zone_symbol__propagationStopped);i++)invokeTask(copyTasks[i],target,event)}},globalZoneAwareCaptureCallback=function(event){if(!(event=event||_global.event))return;const target=this||event.target||_global,tasks=target[zoneSymbolEventNames$1[event.type].true];if(tasks)if(1===tasks.length)invokeTask(tasks[0],target,event);else{const copyTasks=tasks.slice();for(let i=0;i<copyTasks.length&&(!event||!0!==event.__zone_symbol__propagationStopped);i++)invokeTask(copyTasks[i],target,event)}};function patchEventTargetMethods(obj,patchOptions){if(!obj)return!1;let useGlobalCallback=!0;patchOptions&&void 0!==patchOptions.useG&&(useGlobalCallback=patchOptions.useG);const validateHandler=patchOptions&&patchOptions.vh;let checkDuplicate=!0;patchOptions&&void 0!==patchOptions.chkDup&&(checkDuplicate=patchOptions.chkDup);let returnTarget=!1;patchOptions&&void 0!==patchOptions.rt&&(returnTarget=patchOptions.rt);let proto=obj;for(;proto&&!proto.hasOwnProperty(ADD_EVENT_LISTENER);)proto=ObjectGetPrototypeOf(proto);if(!proto&&obj[ADD_EVENT_LISTENER]&&(proto=obj),!proto)return!1;if(proto[zoneSymbolAddEventListener])return!1;const eventNameToString=patchOptions&&patchOptions.eventNameToString,taskData={},nativeAddEventListener=proto[zoneSymbolAddEventListener]=proto[ADD_EVENT_LISTENER],nativeRemoveEventListener=proto[zoneSymbol(REMOVE_EVENT_LISTENER)]=proto[REMOVE_EVENT_LISTENER],nativeListeners=proto[zoneSymbol(LISTENERS_EVENT_LISTENER)]=proto[LISTENERS_EVENT_LISTENER],nativeRemoveAllListeners=proto[zoneSymbol(REMOVE_ALL_LISTENERS_EVENT_LISTENER)]=proto[REMOVE_ALL_LISTENERS_EVENT_LISTENER];let nativePrependEventListener;function checkIsPassive(task){passiveSupported||\"boolean\"==typeof taskData.options||null==taskData.options||(task.options=!!taskData.options.capture,taskData.options=task.options)}patchOptions&&patchOptions.prepend&&(nativePrependEventListener=proto[zoneSymbol(patchOptions.prepend)]=proto[patchOptions.prepend]);const customSchedule=useGlobalCallback?function(task){if(!taskData.isExisting)return checkIsPassive(task),nativeAddEventListener.call(taskData.target,taskData.eventName,taskData.capture?globalZoneAwareCaptureCallback:globalZoneAwareCallback,taskData.options)}:function(task){return checkIsPassive(task),nativeAddEventListener.call(taskData.target,taskData.eventName,task.invoke,taskData.options)},customCancel=useGlobalCallback?function(task){if(!task.isRemoved){const symbolEventNames=zoneSymbolEventNames$1[task.eventName];let symbolEventName;symbolEventNames&&(symbolEventName=symbolEventNames[task.capture?\"true\":\"false\"]);const existingTasks=symbolEventName&&task.target[symbolEventName];if(existingTasks)for(let i=0;i<existingTasks.length;i++)if(existingTasks[i]===task){existingTasks.splice(i,1),task.isRemoved=!0,0===existingTasks.length&&(task.allRemoved=!0,task.target[symbolEventName]=null);break}}if(task.allRemoved)return nativeRemoveEventListener.call(task.target,task.eventName,task.capture?globalZoneAwareCaptureCallback:globalZoneAwareCallback,task.options)}:function(task){return nativeRemoveEventListener.call(task.target,task.eventName,task.invoke,task.options)},compare=patchOptions&&patchOptions.diff?patchOptions.diff:function(task,delegate){const typeOfDelegate=typeof delegate;return\"function\"===typeOfDelegate&&task.callback===delegate||\"object\"===typeOfDelegate&&task.originalDelegate===delegate},blackListedEvents=Zone[Zone.__symbol__(\"BLACK_LISTED_EVENTS\")],makeAddListener=function(nativeListener,addSource,customScheduleFn,customCancelFn,returnTarget=!1,prepend=!1){return function(){const target=this||_global,eventName=arguments[0];let delegate=arguments[1];if(!delegate)return nativeListener.apply(this,arguments);if(isNode&&\"uncaughtException\"===eventName)return nativeListener.apply(this,arguments);let isHandleEvent=!1;if(\"function\"!=typeof delegate){if(!delegate.handleEvent)return nativeListener.apply(this,arguments);isHandleEvent=!0}if(validateHandler&&!validateHandler(nativeListener,delegate,target,arguments))return;const options=arguments[2];if(blackListedEvents)for(let i=0;i<blackListedEvents.length;i++)if(eventName===blackListedEvents[i])return nativeListener.apply(this,arguments);let capture,once=!1;void 0===options?capture=!1:!0===options?capture=!0:!1===options?capture=!1:(capture=!!options&&!!options.capture,once=!!options&&!!options.once);const zone=Zone.current,symbolEventNames=zoneSymbolEventNames$1[eventName];let symbolEventName;if(symbolEventNames)symbolEventName=symbolEventNames[capture?\"true\":\"false\"];else{const symbol=\"__zone_symbol__\"+(eventNameToString?eventNameToString(eventName):eventName)+\"false\",symbolCapture=\"__zone_symbol__\"+(eventNameToString?eventNameToString(eventName):eventName)+\"true\";zoneSymbolEventNames$1[eventName]={},zoneSymbolEventNames$1[eventName].false=symbol,zoneSymbolEventNames$1[eventName].true=symbolCapture,symbolEventName=capture?symbolCapture:symbol}let source,existingTasks=target[symbolEventName],isExisting=!1;if(existingTasks){if(isExisting=!0,checkDuplicate)for(let i=0;i<existingTasks.length;i++)if(compare(existingTasks[i],delegate))return}else existingTasks=target[symbolEventName]=[];const constructorName=target.constructor.name,targetSource=globalSources[constructorName];targetSource&&(source=targetSource[eventName]),source||(source=constructorName+addSource+(eventNameToString?eventNameToString(eventName):eventName)),taskData.options=options,once&&(taskData.options.once=!1),taskData.target=target,taskData.capture=capture,taskData.eventName=eventName,taskData.isExisting=isExisting;const data=useGlobalCallback?OPTIMIZED_ZONE_EVENT_TASK_DATA:void 0;data&&(data.taskData=taskData);const task=zone.scheduleEventTask(source,delegate,data,customScheduleFn,customCancelFn);return taskData.target=null,data&&(data.taskData=null),once&&(options.once=!0),(passiveSupported||\"boolean\"!=typeof task.options)&&(task.options=options),task.target=target,task.capture=capture,task.eventName=eventName,isHandleEvent&&(task.originalDelegate=delegate),prepend?existingTasks.unshift(task):existingTasks.push(task),returnTarget?target:void 0}};return proto[ADD_EVENT_LISTENER]=makeAddListener(nativeAddEventListener,ADD_EVENT_LISTENER_SOURCE,customSchedule,customCancel,returnTarget),nativePrependEventListener&&(proto.prependListener=makeAddListener(nativePrependEventListener,\".prependListener:\",(function(task){return nativePrependEventListener.call(taskData.target,taskData.eventName,task.invoke,taskData.options)}),customCancel,returnTarget,!0)),proto[REMOVE_EVENT_LISTENER]=function(){const target=this||_global,eventName=arguments[0],options=arguments[2];let capture;capture=void 0!==options&&(!0===options||!1!==options&&!!options&&!!options.capture);const delegate=arguments[1];if(!delegate)return nativeRemoveEventListener.apply(this,arguments);if(validateHandler&&!validateHandler(nativeRemoveEventListener,delegate,target,arguments))return;const symbolEventNames=zoneSymbolEventNames$1[eventName];let symbolEventName;symbolEventNames&&(symbolEventName=symbolEventNames[capture?\"true\":\"false\"]);const existingTasks=symbolEventName&&target[symbolEventName];if(existingTasks)for(let i=0;i<existingTasks.length;i++){const existingTask=existingTasks[i];if(compare(existingTask,delegate))return existingTasks.splice(i,1),existingTask.isRemoved=!0,0===existingTasks.length&&(existingTask.allRemoved=!0,target[symbolEventName]=null),existingTask.zone.cancelTask(existingTask),returnTarget?target:void 0}return nativeRemoveEventListener.apply(this,arguments)},proto[LISTENERS_EVENT_LISTENER]=function(){const target=this||_global,eventName=arguments[0],listeners=[],tasks=findEventTasks(target,eventNameToString?eventNameToString(eventName):eventName);for(let i=0;i<tasks.length;i++){const task=tasks[i];listeners.push(task.originalDelegate?task.originalDelegate:task.callback)}return listeners},proto[REMOVE_ALL_LISTENERS_EVENT_LISTENER]=function(){const target=this||_global,eventName=arguments[0];if(eventName){const symbolEventNames=zoneSymbolEventNames$1[eventName];if(symbolEventNames){const tasks=target[symbolEventNames.false],captureTasks=target[symbolEventNames.true];if(tasks){const removeTasks=tasks.slice();for(let i=0;i<removeTasks.length;i++){const task=removeTasks[i];this[REMOVE_EVENT_LISTENER].call(this,eventName,task.originalDelegate?task.originalDelegate:task.callback,task.options)}}if(captureTasks){const removeTasks=captureTasks.slice();for(let i=0;i<removeTasks.length;i++){const task=removeTasks[i];this[REMOVE_EVENT_LISTENER].call(this,eventName,task.originalDelegate?task.originalDelegate:task.callback,task.options)}}}}else{const keys=Object.keys(target);for(let i=0;i<keys.length;i++){const match=EVENT_NAME_SYMBOL_REGX.exec(keys[i]);let evtName=match&&match[1];evtName&&\"removeListener\"!==evtName&&this[REMOVE_ALL_LISTENERS_EVENT_LISTENER].call(this,evtName)}this[REMOVE_ALL_LISTENERS_EVENT_LISTENER].call(this,\"removeListener\")}if(returnTarget)return this},attachOriginToPatched(proto[ADD_EVENT_LISTENER],nativeAddEventListener),attachOriginToPatched(proto[REMOVE_EVENT_LISTENER],nativeRemoveEventListener),nativeRemoveAllListeners&&attachOriginToPatched(proto[REMOVE_ALL_LISTENERS_EVENT_LISTENER],nativeRemoveAllListeners),nativeListeners&&attachOriginToPatched(proto[LISTENERS_EVENT_LISTENER],nativeListeners),!0}let results=[];for(let i=0;i<apis.length;i++)results[i]=patchEventTargetMethods(apis[i],patchOptions);return results}function findEventTasks(target,eventName){const foundTasks=[];for(let prop in target){const match=EVENT_NAME_SYMBOL_REGX.exec(prop);let evtName=match&&match[1];if(evtName&&(!eventName||evtName===eventName)){const tasks=target[prop];if(tasks)for(let i=0;i<tasks.length;i++)foundTasks.push(tasks[i])}}return foundTasks}function patchEventPrototype(global,api){const Event=global.Event;Event&&Event.prototype&&api.patchMethod(Event.prototype,\"stopImmediatePropagation\",delegate=>function(self,args){self.__zone_symbol__propagationStopped=!0,delegate&&delegate.apply(self,args)})}function patchCallbacks(api,target,targetName,method,callbacks){const symbol=Zone.__symbol__(method);if(target[symbol])return;const nativeDelegate=target[symbol]=target[method];target[method]=function(name,opts,options){return opts&&opts.prototype&&callbacks.forEach((function(callback){const source=`${targetName}.${method}::`+callback,prototype=opts.prototype;if(prototype.hasOwnProperty(callback)){const descriptor=api.ObjectGetOwnPropertyDescriptor(prototype,callback);descriptor&&descriptor.value?(descriptor.value=api.wrapWithCurrentZone(descriptor.value,source),api._redefineProperty(opts.prototype,callback,descriptor)):prototype[callback]&&(prototype[callback]=api.wrapWithCurrentZone(prototype[callback],source))}else prototype[callback]&&(prototype[callback]=api.wrapWithCurrentZone(prototype[callback],source))})),nativeDelegate.call(target,name,opts,options)},api.attachOriginToPatched(target[method],nativeDelegate)}const zoneSymbol$1=Zone.__symbol__,_defineProperty=Object[zoneSymbol$1(\"defineProperty\")]=Object.defineProperty,_getOwnPropertyDescriptor=Object[zoneSymbol$1(\"getOwnPropertyDescriptor\")]=Object.getOwnPropertyDescriptor,_create=Object.create,unconfigurablesKey=zoneSymbol$1(\"unconfigurables\");function _redefineProperty(obj,prop,desc){const originalConfigurableFlag=desc.configurable;return _tryDefineProperty(obj,prop,desc=rewriteDescriptor(obj,prop,desc),originalConfigurableFlag)}function isUnconfigurable(obj,prop){return obj&&obj[unconfigurablesKey]&&obj[unconfigurablesKey][prop]}function rewriteDescriptor(obj,prop,desc){return Object.isFrozen(desc)||(desc.configurable=!0),desc.configurable||(obj[unconfigurablesKey]||Object.isFrozen(obj)||_defineProperty(obj,unconfigurablesKey,{writable:!0,value:{}}),obj[unconfigurablesKey]&&(obj[unconfigurablesKey][prop]=!0)),desc}function _tryDefineProperty(obj,prop,desc,originalConfigurableFlag){try{return _defineProperty(obj,prop,desc)}catch(error){if(!desc.configurable)throw error;void 0===originalConfigurableFlag?delete desc.configurable:desc.configurable=originalConfigurableFlag;try{return _defineProperty(obj,prop,desc)}catch(error){let descJson=null;try{descJson=JSON.stringify(desc)}catch(error){descJson=desc.toString()}console.log(`Attempting to configure '${prop}' with descriptor '${descJson}' on object '${obj}' and got error, giving up: ${error}`)}}}const windowEventNames=[\"absolutedeviceorientation\",\"afterinput\",\"afterprint\",\"appinstalled\",\"beforeinstallprompt\",\"beforeprint\",\"beforeunload\",\"devicelight\",\"devicemotion\",\"deviceorientation\",\"deviceorientationabsolute\",\"deviceproximity\",\"hashchange\",\"languagechange\",\"message\",\"mozbeforepaint\",\"offline\",\"online\",\"paint\",\"pageshow\",\"pagehide\",\"popstate\",\"rejectionhandled\",\"storage\",\"unhandledrejection\",\"unload\",\"userproximity\",\"vrdisplyconnected\",\"vrdisplaydisconnected\",\"vrdisplaypresentchange\"],mediaElementEventNames=[\"encrypted\",\"waitingforkey\",\"msneedkey\",\"mozinterruptbegin\",\"mozinterruptend\"],frameEventNames=[\"load\"],frameSetEventNames=[\"blur\",\"error\",\"focus\",\"load\",\"resize\",\"scroll\",\"messageerror\"],marqueeEventNames=[\"bounce\",\"finish\",\"start\"],XMLHttpRequestEventNames=[\"loadstart\",\"progress\",\"abort\",\"error\",\"load\",\"progress\",\"timeout\",\"loadend\",\"readystatechange\"],IDBIndexEventNames=[\"upgradeneeded\",\"complete\",\"abort\",\"success\",\"error\",\"blocked\",\"versionchange\",\"close\"],websocketEventNames=[\"close\",\"error\",\"open\",\"message\"],workerEventNames=[\"error\",\"message\"],eventNames=[\"abort\",\"animationcancel\",\"animationend\",\"animationiteration\",\"auxclick\",\"beforeinput\",\"blur\",\"cancel\",\"canplay\",\"canplaythrough\",\"change\",\"compositionstart\",\"compositionupdate\",\"compositionend\",\"cuechange\",\"click\",\"close\",\"contextmenu\",\"curechange\",\"dblclick\",\"drag\",\"dragend\",\"dragenter\",\"dragexit\",\"dragleave\",\"dragover\",\"drop\",\"durationchange\",\"emptied\",\"ended\",\"error\",\"focus\",\"focusin\",\"focusout\",\"gotpointercapture\",\"input\",\"invalid\",\"keydown\",\"keypress\",\"keyup\",\"load\",\"loadstart\",\"loadeddata\",\"loadedmetadata\",\"lostpointercapture\",\"mousedown\",\"mouseenter\",\"mouseleave\",\"mousemove\",\"mouseout\",\"mouseover\",\"mouseup\",\"mousewheel\",\"orientationchange\",\"pause\",\"play\",\"playing\",\"pointercancel\",\"pointerdown\",\"pointerenter\",\"pointerleave\",\"pointerlockchange\",\"mozpointerlockchange\",\"webkitpointerlockerchange\",\"pointerlockerror\",\"mozpointerlockerror\",\"webkitpointerlockerror\",\"pointermove\",\"pointout\",\"pointerover\",\"pointerup\",\"progress\",\"ratechange\",\"reset\",\"resize\",\"scroll\",\"seeked\",\"seeking\",\"select\",\"selectionchange\",\"selectstart\",\"show\",\"sort\",\"stalled\",\"submit\",\"suspend\",\"timeupdate\",\"volumechange\",\"touchcancel\",\"touchmove\",\"touchstart\",\"touchend\",\"transitioncancel\",\"transitionend\",\"waiting\",\"wheel\"].concat([\"webglcontextrestored\",\"webglcontextlost\",\"webglcontextcreationerror\"],[\"autocomplete\",\"autocompleteerror\"],[\"toggle\"],[\"afterscriptexecute\",\"beforescriptexecute\",\"DOMContentLoaded\",\"freeze\",\"fullscreenchange\",\"mozfullscreenchange\",\"webkitfullscreenchange\",\"msfullscreenchange\",\"fullscreenerror\",\"mozfullscreenerror\",\"webkitfullscreenerror\",\"msfullscreenerror\",\"readystatechange\",\"visibilitychange\",\"resume\"],windowEventNames,[\"beforecopy\",\"beforecut\",\"beforepaste\",\"copy\",\"cut\",\"paste\",\"dragstart\",\"loadend\",\"animationstart\",\"search\",\"transitionrun\",\"transitionstart\",\"webkitanimationend\",\"webkitanimationiteration\",\"webkitanimationstart\",\"webkittransitionend\"],[\"activate\",\"afterupdate\",\"ariarequest\",\"beforeactivate\",\"beforedeactivate\",\"beforeeditfocus\",\"beforeupdate\",\"cellchange\",\"controlselect\",\"dataavailable\",\"datasetchanged\",\"datasetcomplete\",\"errorupdate\",\"filterchange\",\"layoutcomplete\",\"losecapture\",\"move\",\"moveend\",\"movestart\",\"propertychange\",\"resizeend\",\"resizestart\",\"rowenter\",\"rowexit\",\"rowsdelete\",\"rowsinserted\",\"command\",\"compassneedscalibration\",\"deactivate\",\"help\",\"mscontentzoom\",\"msmanipulationstatechanged\",\"msgesturechange\",\"msgesturedoubletap\",\"msgestureend\",\"msgesturehold\",\"msgesturestart\",\"msgesturetap\",\"msgotpointercapture\",\"msinertiastart\",\"mslostpointercapture\",\"mspointercancel\",\"mspointerdown\",\"mspointerenter\",\"mspointerhover\",\"mspointerleave\",\"mspointermove\",\"mspointerout\",\"mspointerover\",\"mspointerup\",\"pointerout\",\"mssitemodejumplistitemremoved\",\"msthumbnailclick\",\"stop\",\"storagecommit\"]);function filterProperties(target,onProperties,ignoreProperties){if(!ignoreProperties||0===ignoreProperties.length)return onProperties;const tip=ignoreProperties.filter(ip=>ip.target===target);if(!tip||0===tip.length)return onProperties;const targetIgnoreProperties=tip[0].ignoreProperties;return onProperties.filter(op=>-1===targetIgnoreProperties.indexOf(op))}function patchFilteredProperties(target,onProperties,ignoreProperties,prototype){target&&patchOnProperties(target,filterProperties(target,onProperties,ignoreProperties),prototype)}function propertyDescriptorPatch(api,_global){if(isNode&&!isMix)return;if(Zone[api.symbol(\"patchEvents\")])return;const supportsWebSocket=\"undefined\"!=typeof WebSocket,ignoreProperties=_global.__Zone_ignore_on_properties;if(isBrowser){const internalWindow=window,ignoreErrorProperties=isIE?[{target:internalWindow,ignoreProperties:[\"error\"]}]:[];patchFilteredProperties(internalWindow,eventNames.concat([\"messageerror\"]),ignoreProperties?ignoreProperties.concat(ignoreErrorProperties):ignoreProperties,ObjectGetPrototypeOf(internalWindow)),patchFilteredProperties(Document.prototype,eventNames,ignoreProperties),void 0!==internalWindow.SVGElement&&patchFilteredProperties(internalWindow.SVGElement.prototype,eventNames,ignoreProperties),patchFilteredProperties(Element.prototype,eventNames,ignoreProperties),patchFilteredProperties(HTMLElement.prototype,eventNames,ignoreProperties),patchFilteredProperties(HTMLMediaElement.prototype,mediaElementEventNames,ignoreProperties),patchFilteredProperties(HTMLFrameSetElement.prototype,windowEventNames.concat(frameSetEventNames),ignoreProperties),patchFilteredProperties(HTMLBodyElement.prototype,windowEventNames.concat(frameSetEventNames),ignoreProperties),patchFilteredProperties(HTMLFrameElement.prototype,frameEventNames,ignoreProperties),patchFilteredProperties(HTMLIFrameElement.prototype,frameEventNames,ignoreProperties);const HTMLMarqueeElement=internalWindow.HTMLMarqueeElement;HTMLMarqueeElement&&patchFilteredProperties(HTMLMarqueeElement.prototype,marqueeEventNames,ignoreProperties);const Worker=internalWindow.Worker;Worker&&patchFilteredProperties(Worker.prototype,workerEventNames,ignoreProperties)}const XMLHttpRequest=_global.XMLHttpRequest;XMLHttpRequest&&patchFilteredProperties(XMLHttpRequest.prototype,XMLHttpRequestEventNames,ignoreProperties);const XMLHttpRequestEventTarget=_global.XMLHttpRequestEventTarget;XMLHttpRequestEventTarget&&patchFilteredProperties(XMLHttpRequestEventTarget&&XMLHttpRequestEventTarget.prototype,XMLHttpRequestEventNames,ignoreProperties),\"undefined\"!=typeof IDBIndex&&(patchFilteredProperties(IDBIndex.prototype,IDBIndexEventNames,ignoreProperties),patchFilteredProperties(IDBRequest.prototype,IDBIndexEventNames,ignoreProperties),patchFilteredProperties(IDBOpenDBRequest.prototype,IDBIndexEventNames,ignoreProperties),patchFilteredProperties(IDBDatabase.prototype,IDBIndexEventNames,ignoreProperties),patchFilteredProperties(IDBTransaction.prototype,IDBIndexEventNames,ignoreProperties),patchFilteredProperties(IDBCursor.prototype,IDBIndexEventNames,ignoreProperties)),supportsWebSocket&&patchFilteredProperties(WebSocket.prototype,websocketEventNames,ignoreProperties)}Zone.__load_patch(\"util\",(global,Zone,api)=>{api.patchOnProperties=patchOnProperties,api.patchMethod=patchMethod,api.bindArguments=bindArguments,api.patchMacroTask=patchMacroTask;const SYMBOL_BLACK_LISTED_EVENTS=Zone.__symbol__(\"BLACK_LISTED_EVENTS\"),SYMBOL_UNPATCHED_EVENTS=Zone.__symbol__(\"UNPATCHED_EVENTS\");global[SYMBOL_UNPATCHED_EVENTS]&&(global[SYMBOL_BLACK_LISTED_EVENTS]=global[SYMBOL_UNPATCHED_EVENTS]),global[SYMBOL_BLACK_LISTED_EVENTS]&&(Zone[SYMBOL_BLACK_LISTED_EVENTS]=Zone[SYMBOL_UNPATCHED_EVENTS]=global[SYMBOL_BLACK_LISTED_EVENTS]),api.patchEventPrototype=patchEventPrototype,api.patchEventTarget=patchEventTarget,api.isIEOrEdge=isIEOrEdge,api.ObjectDefineProperty=ObjectDefineProperty,api.ObjectGetOwnPropertyDescriptor=ObjectGetOwnPropertyDescriptor,api.ObjectCreate=ObjectCreate,api.ArraySlice=ArraySlice,api.patchClass=patchClass,api.wrapWithCurrentZone=wrapWithCurrentZone,api.filterProperties=filterProperties,api.attachOriginToPatched=attachOriginToPatched,api._redefineProperty=_redefineProperty,api.patchCallbacks=patchCallbacks,api.getGlobalObjects=()=>({globalSources:globalSources,zoneSymbolEventNames:zoneSymbolEventNames$1,eventNames:eventNames,isBrowser:isBrowser,isMix:isMix,isNode:isNode,TRUE_STR:\"true\",FALSE_STR:\"false\",ZONE_SYMBOL_PREFIX:\"__zone_symbol__\",ADD_EVENT_LISTENER_STR:\"addEventListener\",REMOVE_EVENT_LISTENER_STR:\"removeEventListener\"})});const taskSymbol=zoneSymbol(\"zoneTask\");function patchTimer(window,setName,cancelName,nameSuffix){let setNative=null,clearNative=null;cancelName+=nameSuffix;const tasksByHandleId={};function scheduleTask(task){const data=task.data;return data.args[0]=function(){try{task.invoke.apply(this,arguments)}finally{task.data&&task.data.isPeriodic||(\"number\"==typeof data.handleId?delete tasksByHandleId[data.handleId]:data.handleId&&(data.handleId[taskSymbol]=null))}},data.handleId=setNative.apply(window,data.args),task}function clearTask(task){return clearNative(task.data.handleId)}setNative=patchMethod(window,setName+=nameSuffix,delegate=>function(self,args){if(\"function\"==typeof args[0]){const task=scheduleMacroTaskWithCurrentZone(setName,args[0],{isPeriodic:\"Interval\"===nameSuffix,delay:\"Timeout\"===nameSuffix||\"Interval\"===nameSuffix?args[1]||0:void 0,args:args},scheduleTask,clearTask);if(!task)return task;const handle=task.data.handleId;return\"number\"==typeof handle?tasksByHandleId[handle]=task:handle&&(handle[taskSymbol]=task),handle&&handle.ref&&handle.unref&&\"function\"==typeof handle.ref&&\"function\"==typeof handle.unref&&(task.ref=handle.ref.bind(handle),task.unref=handle.unref.bind(handle)),\"number\"==typeof handle||handle?handle:task}return delegate.apply(window,args)}),clearNative=patchMethod(window,cancelName,delegate=>function(self,args){const id=args[0];let task;\"number\"==typeof id?task=tasksByHandleId[id]:(task=id&&id[taskSymbol],task||(task=id)),task&&\"string\"==typeof task.type?\"notScheduled\"!==task.state&&(task.cancelFn&&task.data.isPeriodic||0===task.runCount)&&(\"number\"==typeof id?delete tasksByHandleId[id]:id&&(id[taskSymbol]=null),task.zone.cancelTask(task)):delegate.apply(window,args)})}function eventTargetPatch(_global,api){if(Zone[api.symbol(\"patchEventTarget\")])return;const{eventNames:eventNames,zoneSymbolEventNames:zoneSymbolEventNames,TRUE_STR:TRUE_STR,FALSE_STR:FALSE_STR,ZONE_SYMBOL_PREFIX:ZONE_SYMBOL_PREFIX}=api.getGlobalObjects();for(let i=0;i<eventNames.length;i++){const eventName=eventNames[i],symbol=ZONE_SYMBOL_PREFIX+(eventName+FALSE_STR),symbolCapture=ZONE_SYMBOL_PREFIX+(eventName+TRUE_STR);zoneSymbolEventNames[eventName]={},zoneSymbolEventNames[eventName][FALSE_STR]=symbol,zoneSymbolEventNames[eventName][TRUE_STR]=symbolCapture}const EVENT_TARGET=_global.EventTarget;return EVENT_TARGET&&EVENT_TARGET.prototype?(api.patchEventTarget(_global,[EVENT_TARGET&&EVENT_TARGET.prototype]),!0):void 0}Zone.__load_patch(\"legacy\",global=>{const legacyPatch=global[Zone.__symbol__(\"legacyPatch\")];legacyPatch&&legacyPatch()}),Zone.__load_patch(\"timers\",global=>{patchTimer(global,\"set\",\"clear\",\"Timeout\"),patchTimer(global,\"set\",\"clear\",\"Interval\"),patchTimer(global,\"set\",\"clear\",\"Immediate\")}),Zone.__load_patch(\"requestAnimationFrame\",global=>{patchTimer(global,\"request\",\"cancel\",\"AnimationFrame\"),patchTimer(global,\"mozRequest\",\"mozCancel\",\"AnimationFrame\"),patchTimer(global,\"webkitRequest\",\"webkitCancel\",\"AnimationFrame\")}),Zone.__load_patch(\"blocking\",(global,Zone)=>{const blockingMethods=[\"alert\",\"prompt\",\"confirm\"];for(let i=0;i<blockingMethods.length;i++)patchMethod(global,blockingMethods[i],(delegate,symbol,name)=>function(s,args){return Zone.current.run(delegate,global,args,name)})}),Zone.__load_patch(\"EventTarget\",(global,Zone,api)=>{!function(global,api){api.patchEventPrototype(global,api)}(global,api),eventTargetPatch(global,api);const XMLHttpRequestEventTarget=global.XMLHttpRequestEventTarget;XMLHttpRequestEventTarget&&XMLHttpRequestEventTarget.prototype&&api.patchEventTarget(global,[XMLHttpRequestEventTarget.prototype]),patchClass(\"MutationObserver\"),patchClass(\"WebKitMutationObserver\"),patchClass(\"IntersectionObserver\"),patchClass(\"FileReader\")}),Zone.__load_patch(\"on_property\",(global,Zone,api)=>{propertyDescriptorPatch(api,global),Object.defineProperty=function(obj,prop,desc){if(isUnconfigurable(obj,prop))throw new TypeError(\"Cannot assign to read only property '\"+prop+\"' of \"+obj);const originalConfigurableFlag=desc.configurable;return\"prototype\"!==prop&&(desc=rewriteDescriptor(obj,prop,desc)),_tryDefineProperty(obj,prop,desc,originalConfigurableFlag)},Object.defineProperties=function(obj,props){return Object.keys(props).forEach((function(prop){Object.defineProperty(obj,prop,props[prop])})),obj},Object.create=function(obj,proto){return\"object\"!=typeof proto||Object.isFrozen(proto)||Object.keys(proto).forEach((function(prop){proto[prop]=rewriteDescriptor(obj,prop,proto[prop])})),_create(obj,proto)},Object.getOwnPropertyDescriptor=function(obj,prop){const desc=_getOwnPropertyDescriptor(obj,prop);return desc&&isUnconfigurable(obj,prop)&&(desc.configurable=!1),desc}}),Zone.__load_patch(\"customElements\",(global,Zone,api)=>{!function(_global,api){const{isBrowser:isBrowser,isMix:isMix}=api.getGlobalObjects();(isBrowser||isMix)&&_global.customElements&&\"customElements\"in _global&&api.patchCallbacks(api,_global.customElements,\"customElements\",\"define\",[\"connectedCallback\",\"disconnectedCallback\",\"adoptedCallback\",\"attributeChangedCallback\"])}(global,api)}),Zone.__load_patch(\"XHR\",(global,Zone)=>{!function(window){const XMLHttpRequest=window.XMLHttpRequest;if(!XMLHttpRequest)return;const XMLHttpRequestPrototype=XMLHttpRequest.prototype;let oriAddListener=XMLHttpRequestPrototype[ZONE_SYMBOL_ADD_EVENT_LISTENER],oriRemoveListener=XMLHttpRequestPrototype[ZONE_SYMBOL_REMOVE_EVENT_LISTENER];if(!oriAddListener){const XMLHttpRequestEventTarget=window.XMLHttpRequestEventTarget;if(XMLHttpRequestEventTarget){const XMLHttpRequestEventTargetPrototype=XMLHttpRequestEventTarget.prototype;oriAddListener=XMLHttpRequestEventTargetPrototype[ZONE_SYMBOL_ADD_EVENT_LISTENER],oriRemoveListener=XMLHttpRequestEventTargetPrototype[ZONE_SYMBOL_REMOVE_EVENT_LISTENER]}}function scheduleTask(task){const data=task.data,target=data.target;target[XHR_SCHEDULED]=!1,target[XHR_ERROR_BEFORE_SCHEDULED]=!1;const listener=target[XHR_LISTENER];oriAddListener||(oriAddListener=target[ZONE_SYMBOL_ADD_EVENT_LISTENER],oriRemoveListener=target[ZONE_SYMBOL_REMOVE_EVENT_LISTENER]),listener&&oriRemoveListener.call(target,\"readystatechange\",listener);const newListener=target[XHR_LISTENER]=()=>{if(target.readyState===target.DONE)if(!data.aborted&&target[XHR_SCHEDULED]&&\"scheduled\"===task.state){const loadTasks=target.__zone_symbol__loadfalse;if(loadTasks&&loadTasks.length>0){const oriInvoke=task.invoke;task.invoke=function(){const loadTasks=target.__zone_symbol__loadfalse;for(let i=0;i<loadTasks.length;i++)loadTasks[i]===task&&loadTasks.splice(i,1);data.aborted||\"scheduled\"!==task.state||oriInvoke.call(task)},loadTasks.push(task)}else task.invoke()}else data.aborted||!1!==target[XHR_SCHEDULED]||(target[XHR_ERROR_BEFORE_SCHEDULED]=!0)};return oriAddListener.call(target,\"readystatechange\",newListener),target[XHR_TASK]||(target[XHR_TASK]=task),sendNative.apply(target,data.args),target[XHR_SCHEDULED]=!0,task}function placeholderCallback(){}function clearTask(task){const data=task.data;return data.aborted=!0,abortNative.apply(data.target,data.args)}const openNative=patchMethod(XMLHttpRequestPrototype,\"open\",()=>function(self,args){return self[XHR_SYNC]=0==args[2],self[XHR_URL]=args[1],openNative.apply(self,args)}),fetchTaskAborting=zoneSymbol(\"fetchTaskAborting\"),fetchTaskScheduling=zoneSymbol(\"fetchTaskScheduling\"),sendNative=patchMethod(XMLHttpRequestPrototype,\"send\",()=>function(self,args){if(!0===Zone.current[fetchTaskScheduling])return sendNative.apply(self,args);if(self[XHR_SYNC])return sendNative.apply(self,args);{const options={target:self,url:self[XHR_URL],isPeriodic:!1,args:args,aborted:!1},task=scheduleMacroTaskWithCurrentZone(\"XMLHttpRequest.send\",placeholderCallback,options,scheduleTask,clearTask);self&&!0===self[XHR_ERROR_BEFORE_SCHEDULED]&&!options.aborted&&\"scheduled\"===task.state&&task.invoke()}}),abortNative=patchMethod(XMLHttpRequestPrototype,\"abort\",()=>function(self,args){const task=self[XHR_TASK];if(task&&\"string\"==typeof task.type){if(null==task.cancelFn||task.data&&task.data.aborted)return;task.zone.cancelTask(task)}else if(!0===Zone.current[fetchTaskAborting])return abortNative.apply(self,args)})}(global);const XHR_TASK=zoneSymbol(\"xhrTask\"),XHR_SYNC=zoneSymbol(\"xhrSync\"),XHR_LISTENER=zoneSymbol(\"xhrListener\"),XHR_SCHEDULED=zoneSymbol(\"xhrScheduled\"),XHR_URL=zoneSymbol(\"xhrURL\"),XHR_ERROR_BEFORE_SCHEDULED=zoneSymbol(\"xhrErrorBeforeScheduled\")}),Zone.__load_patch(\"geolocation\",global=>{global.navigator&&global.navigator.geolocation&&function(prototype,fnNames){const source=prototype.constructor.name;for(let i=0;i<fnNames.length;i++){const name=fnNames[i],delegate=prototype[name];if(delegate){if(!isPropertyWritable(ObjectGetOwnPropertyDescriptor(prototype,name)))continue;prototype[name]=(delegate=>{const patched=function(){return delegate.apply(this,bindArguments(arguments,source+\".\"+name))};return attachOriginToPatched(patched,delegate),patched})(delegate)}}}(global.navigator.geolocation,[\"getCurrentPosition\",\"watchPosition\"])}),Zone.__load_patch(\"PromiseRejectionEvent\",(global,Zone)=>{function findPromiseRejectionHandler(evtName){return function(e){findEventTasks(global,evtName).forEach(eventTask=>{const PromiseRejectionEvent=global.PromiseRejectionEvent;if(PromiseRejectionEvent){const evt=new PromiseRejectionEvent(evtName,{promise:e.promise,reason:e.rejection});eventTask.invoke(evt)}})}}global.PromiseRejectionEvent&&(Zone[zoneSymbol(\"unhandledPromiseRejectionHandler\")]=findPromiseRejectionHandler(\"unhandledrejection\"),Zone[zoneSymbol(\"rejectionHandledHandler\")]=findPromiseRejectionHandler(\"rejectionhandled\"))})}},[[2,0]]]);", "extractedComments": ["/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */"]}