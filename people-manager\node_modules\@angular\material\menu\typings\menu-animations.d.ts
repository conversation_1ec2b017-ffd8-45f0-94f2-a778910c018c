/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { AnimationTriggerMetadata } from '@angular/animations';
/**
 * Animations used by the mat-menu component.
 * Animation duration and timing values are based on:
 * https://material.io/guidelines/components/menus.html#menus-usage
 * @docs-private
 */
export declare const matMenuAnimations: {
    readonly transformMenu: AnimationTriggerMetadata;
    readonly fadeInItems: AnimationTriggerMetadata;
};
/**
 * @deprecated
 * @breaking-change 8.0.0
 * @docs-private
 */
export declare const fadeInItems: AnimationTriggerMetadata;
/**
 * @deprecated
 * @breaking-change 8.0.0
 * @docs-private
 */
export declare const transformMenu: AnimationTriggerMetadata;
