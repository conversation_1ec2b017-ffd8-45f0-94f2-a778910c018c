# 🔧 Node.js Compatibility Fix for Vercel Deployment

## 🚨 Issue: OpenSSL Digital Envelope Error

**Error:** `error:0308010C:digital envelope routines::unsupported`

**Cause:** Vercel uses Node.js 18+ by default, but Angular 8 uses older webpack that doesn't support the new OpenSSL 3.0 crypto algorithms.

---

## ✅ Solutions Applied

### **1. Node.js Version Specification**

#### **package.json:**
```json
{
  "engines": {
    "node": "16.x"
  }
}
```

#### **.nvmrc:**
```
16
```

### **2. Vercel Configuration**

#### **vercel.json:**
```json
{
  "buildCommand": "npm run build",
  "outputDirectory": "dist/people-manager",
  "installCommand": "npm install",
  "framework": "angular",
  "build": {
    "env": {
      "NODE_OPTIONS": "--openssl-legacy-provider"
    }
  }
}
```

---

## 🚀 Updated Vercel Settings

### **Use These Exact Settings in Vercel Dashboard:**

#### **Framework Settings:**
```
Framework Preset: Angular
Root Directory: ./
Build Command: npm run build
Output Directory: dist/people-manager
Install Command: npm install
```

#### **Environment Variables:**
| Key | Value |
|-----|-------|
| `NODE_OPTIONS` | `--openssl-legacy-provider` |
| `NODE_VERSION` | `16` |

---

## 🔧 Alternative Solutions

### **Option 1: Environment Variables Only**
If the vercel.json doesn't work, add these environment variables in Vercel dashboard:

```
NODE_OPTIONS=--openssl-legacy-provider
NODE_VERSION=16
```

### **Option 2: Build Command Override**
In Vercel dashboard, use this build command:
```
NODE_OPTIONS=--openssl-legacy-provider npm run build
```

### **Option 3: Package.json Scripts (Cross-platform)**
Install cross-env for Windows compatibility:
```bash
npm install --save-dev cross-env
```

Then update package.json:
```json
{
  "scripts": {
    "build": "cross-env NODE_OPTIONS=--openssl-legacy-provider ng build --prod",
    "vercel-build": "cross-env NODE_OPTIONS=--openssl-legacy-provider ng build --prod"
  }
}
```

---

## 📋 Deployment Checklist

### **Before Deploying:**
- [x] Node.js version specified (16.x)
- [x] .nvmrc file created
- [x] vercel.json updated with NODE_OPTIONS
- [x] Environment variables ready

### **In Vercel Dashboard:**
- [ ] Set Framework to Angular
- [ ] Set Build Command to `npm run build`
- [ ] Set Output Directory to `dist/people-manager`
- [ ] Add Environment Variable: `NODE_OPTIONS=--openssl-legacy-provider`
- [ ] Add Environment Variable: `NODE_VERSION=16`

---

## 🎯 Why This Happens

### **Node.js Version Changes:**
- **Node.js 16:** Uses OpenSSL 1.1.1 (compatible with Angular 8)
- **Node.js 17+:** Uses OpenSSL 3.0 (breaks older webpack)

### **Angular 8 Limitations:**
- Uses webpack 4.x
- Older crypto algorithms
- Not compatible with OpenSSL 3.0

### **The Fix:**
- Force Node.js 16 usage
- Enable legacy OpenSSL provider
- Maintain compatibility

---

## 🚀 Deploy Now!

With these fixes, your deployment should work:

```bash
# Deploy with CLI
vercel --prod
```

Or push to Git and let Vercel auto-deploy with the new configuration.

---

## 🔍 Troubleshooting

### **If Still Getting Errors:**

#### **1. Check Node Version in Vercel Logs**
Look for: `Node.js version: 16.x.x`

#### **2. Verify Environment Variables**
In Vercel dashboard, ensure:
- `NODE_OPTIONS=--openssl-legacy-provider`
- `NODE_VERSION=16`

#### **3. Clear Vercel Cache**
In Vercel dashboard:
- Go to Settings → Functions
- Clear deployment cache

#### **4. Alternative Build Command**
Try this in Vercel dashboard:
```
NODE_OPTIONS=--openssl-legacy-provider NODE_VERSION=16 npm run build
```

---

## ✅ Expected Result

After applying these fixes:
- ✅ Build completes successfully
- ✅ No OpenSSL errors
- ✅ Angular 8 app deploys correctly
- ✅ All features work as expected

**Your People Manager will be live without Node.js compatibility issues!** 🎉
