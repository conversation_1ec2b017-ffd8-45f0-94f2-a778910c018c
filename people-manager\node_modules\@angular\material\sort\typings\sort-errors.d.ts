/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
/** @docs-private */
export declare function getSortDuplicateSortableIdError(id: string): Error;
/** @docs-private */
export declare function getSortHeaderNotContainedWithinSortError(): Error;
/** @docs-private */
export declare function getSortHeaderMissingIdError(): Error;
/** @docs-private */
export declare function getSortInvalidDirectionError(direction: string): Error;
