<div class="people-list-container">
  <mat-card>
    <mat-card-header>
      <mat-card-title>People Management</mat-card-title>
      <mat-card-subtitle>Manage your list of people</mat-card-subtitle>
    </mat-card-header>

    <mat-card-content>
      <div class="actions-bar">
        <button mat-raised-button color="primary" (click)="addPerson()">
          <mat-icon>add</mat-icon>
          Add Person
        </button>
      </div>

      <div *ngIf="loading" class="loading-container">
        <mat-spinner></mat-spinner>
        <p>Loading people...</p>
      </div>

      <div *ngIf="!loading && people.length === 0" class="no-data">
        <mat-icon>people_outline</mat-icon>
        <p>No people found. Click "Add Person" to get started.</p>
      </div>

      <div *ngIf="!loading && people.length > 0" class="table-container">
        <table mat-table [dataSource]="people" class="people-table">
          <!-- First Name Column -->
          <ng-container matColumnDef="firstName">
            <th mat-header-cell *matHeaderCellDef>First Name</th>
            <td mat-cell *matCellDef="let person">{{person.firstName}}</td>
          </ng-container>

          <!-- Last Name Column -->
          <ng-container matColumnDef="lastName">
            <th mat-header-cell *matHeaderCellDef>Last Name</th>
            <td mat-cell *matCellDef="let person">{{person.lastName}}</td>
          </ng-container>

          <!-- Email Column -->
          <ng-container matColumnDef="email">
            <th mat-header-cell *matHeaderCellDef>Email</th>
            <td mat-cell *matCellDef="let person">{{person.email}}</td>
          </ng-container>

          <!-- Phone Column -->
          <ng-container matColumnDef="phone">
            <th mat-header-cell *matHeaderCellDef>Phone</th>
            <td mat-cell *matCellDef="let person">{{person.phone || 'N/A'}}</td>
          </ng-container>

          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef>Actions</th>
            <td mat-cell *matCellDef="let person">
              <button mat-icon-button color="primary" (click)="editPerson(person)" matTooltip="Edit">
                <mat-icon>edit</mat-icon>
              </button>
              <button mat-icon-button color="warn" (click)="deletePerson(person)" matTooltip="Delete">
                <mat-icon>delete</mat-icon>
              </button>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>
      </div>
    </mat-card-content>
  </mat-card>
</div>
