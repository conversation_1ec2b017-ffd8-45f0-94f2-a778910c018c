<div class="people-list-container">
  <div class="header-section">
    <div class="title-section">
      <h1 class="page-title">
        People Management
      </h1>
      <p class="page-subtitle">Manage your contacts and team members</p>
    </div>
    <div class="actions-section">
      <button mat-fab color="primary" (click)="addPerson()" matTooltip="Add New Person" class="add-fab">
        <mat-icon>add</mat-icon>
      </button>
    </div>
  </div>

  <mat-card class="main-card" [class.loading]="loading">
    <mat-card-content>
      <div *ngIf="loading" class="loading-container">
        <mat-spinner diameter="50"></mat-spinner>
        <p class="loading-text">Loading people...</p>
      </div>

      <div *ngIf="!loading && people.length === 0" class="no-data">
        <div class="no-data-icon">
        </div>
        <h3>No People Found</h3>
        <p>Get started by adding your first person to the system.</p>
        <button mat-raised-button color="primary" (click)="addPerson()">
       
          Add First Person
        </button>
      </div>

      <div *ngIf="!loading && people.length > 0" class="table-container">
        <div class="table-header">
          <h3>All People ({{people.length}})</h3>
          <button mat-stroked-button (click)="loadPeople()" matTooltip="Refresh">

            Refresh
          </button>
        </div>

        <div class="table-wrapper">
          <table mat-table [dataSource]="people" class="people-table mat-elevation-2">
            <!-- Avatar Column -->
            <ng-container matColumnDef="avatar">
              <th mat-header-cell *matHeaderCellDef class="avatar-column"></th>
              <td mat-cell *matCellDef="let person" class="avatar-column">
                <div class="avatar">
                  {{getInitials(person.firstName, person.lastName)}}
                </div>
              </td>
            </ng-container>

            <!-- Name Column -->
            <ng-container matColumnDef="name">
              <th mat-header-cell *matHeaderCellDef>Name & Email</th>
              <td mat-cell *matCellDef="let person" class="name-cell">
                <div class="name-container">
                  <div class="name-row">
                    <!-- <span class="name-label">Name:</span> -->
                    <span class="full-name">{{person.firstName}} {{person.lastName}}</span>
                  </div>
                  <div class="email-row">
                    <!-- <span class="email-label">Email:</span> -->
                    <span class="email-value">{{person.email}}</span>
                  </div>
                </div>
              </td>
            </ng-container>

            <!-- Contact Column -->
            <ng-container matColumnDef="contact">
              <th mat-header-cell *matHeaderCellDef>Contact Information</th>
              <td mat-cell *matCellDef="let person" class="contact-cell">
                <div class="contact-info">
                  <div class="phone-row" *ngIf="person.phone">
                    <span class="contact-label">
                      Phone
                    </span>
                    <span class="contact-value">{{person.phone}}</span>
                  </div>
                  <div class="address-row" *ngIf="person.address">
                    <span class="contact-label">
                      Location
                    </span>
                    <span class="contact-value">{{person.address}}</span>
                  </div>
                  <div class="no-contact" *ngIf="!person.phone && !person.address">
                    <span class="no-contact-text">No contact information available</span>
                  </div>
                </div>
              </td>
            </ng-container>

            <!-- Actions Column -->
            <ng-container matColumnDef="actions">
              <th mat-header-cell *matHeaderCellDef class="actions-column">Actions</th>
              <td mat-cell *matCellDef="let person" class="actions-column">
                <div class="action-buttons">
                  <button mat-icon-button color="primary" (click)="editPerson(person)" matTooltip="Edit Person">
                    <mat-icon>edit</mat-icon>
                  </button>
                  <button mat-icon-button color="warn" (click)="deletePerson(person)" matTooltip="Delete Person">
                    <mat-icon>delete</mat-icon>
                  </button>
                </div>
              </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns" class="table-header-row"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns;" class="table-row"
                [class.highlighted]="row === selectedPerson"></tr>
          </table>
        </div>
      </div>
    </mat-card-content>
  </mat-card>
</div>
