import { Person } from '../../models/person.model';

/**
 * Delete People Utilities
 * Contains helper functions and constants for delete operations
 */

// Constants
export const DELETE_CONSTANTS = {
  // Messages
  MESSAGES: {
    SUCCESS_SINGLE: (name: string) => `${name} has been deleted successfully`,
    SUCCESS_MULTIPLE: (count: number) => `${count} people have been deleted successfully`,
    ERROR_SINGLE: (name: string) => `Failed to delete ${name}. Please try again.`,
    ERROR_MULTIPLE: 'Failed to delete some people. Please try again.',
    CONFIRM_SINGLE: (name: string) => `Are you sure you want to delete ${name}?`,
    CONFIRM_MULTIPLE: (count: number) => `Are you sure you want to delete ${count} people?`,
    INVALID_ID: 'Cannot delete person: Invalid ID',
    NO_SELECTION: 'No people selected for deletion'
  },

  // Snackbar Configuration
  SNACKBAR: {
    SUCCESS_DURATION: 3000,
    ERROR_DURATION: 5000,
    POSITION: {
      horizontal: 'center' as const,
      vertical: 'bottom' as const
    },
    CLASSES: {
      success: ['success-snackbar'],
      error: ['error-snackbar'],
      warning: ['warning-snackbar']
    }
  },

  // Button Configuration
  BUTTONS: {
    ICONS: {
      delete: 'delete',
      deleteForever: 'delete_forever',
      clear: 'clear',
      remove: 'remove_circle'
    },
    COLORS: {
      warn: 'warn',
      primary: 'primary',
      accent: 'accent'
    },
    TEXTS: {
      delete: 'Delete',
      remove: 'Remove',
      deleteSelected: 'Delete Selected',
      deleteAll: 'Delete All'
    }
  }
};

/**
 * Validate if a person can be deleted
 * @param person - The person to validate
 * @returns boolean - Whether the person can be deleted
 */
export function canDeletePerson(person: Person | null | undefined): boolean {
  if (!person) {
    console.warn('DeleteUtils: Cannot delete null/undefined person');
    return false;
  }
  
  if (!person.id) {
    console.warn('DeleteUtils: Cannot delete person without ID:', person);
    return false;
  }
  
  return true;
}

/**
 * Validate if multiple people can be deleted
 * @param people - Array of people to validate
 * @returns object - Validation result with valid people and count
 */
export function canDeleteMultiplePeople(people: Person[]): {
  canDelete: boolean;
  validPeople: Person[];
  invalidPeople: Person[];
  validCount: number;
} {
  if (!people || people.length === 0) {
    return {
      canDelete: false,
      validPeople: [],
      invalidPeople: [],
      validCount: 0
    };
  }

  const validPeople = people.filter(person => canDeletePerson(person));
  const invalidPeople = people.filter(person => !canDeletePerson(person));

  return {
    canDelete: validPeople.length > 0,
    validPeople,
    invalidPeople,
    validCount: validPeople.length
  };
}

/**
 * Get formatted person name
 * @param person - The person to get name for
 * @returns string - Formatted full name
 */
export function getPersonName(person: Person): string {
  if (!person) {
    return 'Unknown Person';
  }
  
  const firstName = person.firstName || '';
  const lastName = person.lastName || '';
  
  if (!firstName && !lastName) {
    return 'Unnamed Person';
  }
  
  return `${firstName} ${lastName}`.trim();
}

/**
 * Get delete confirmation message for a person
 * @param person - The person to get message for
 * @returns string - Confirmation message
 */
export function getDeleteConfirmationMessage(person: Person): string {
  const personName = getPersonName(person);
  return DELETE_CONSTANTS.MESSAGES.CONFIRM_SINGLE(personName);
}

/**
 * Get bulk delete confirmation message
 * @param people - Array of people to delete
 * @returns string - Confirmation message
 */
export function getBulkDeleteConfirmationMessage(people: Person[]): string {
  const validation = canDeleteMultiplePeople(people);
  return DELETE_CONSTANTS.MESSAGES.CONFIRM_MULTIPLE(validation.validCount);
}

/**
 * Get success message for single deletion
 * @param person - The deleted person
 * @returns string - Success message
 */
export function getDeleteSuccessMessage(person: Person): string {
  const personName = getPersonName(person);
  return DELETE_CONSTANTS.MESSAGES.SUCCESS_SINGLE(personName);
}

/**
 * Get success message for bulk deletion
 * @param count - Number of deleted people
 * @returns string - Success message
 */
export function getBulkDeleteSuccessMessage(count: number): string {
  return DELETE_CONSTANTS.MESSAGES.SUCCESS_MULTIPLE(count);
}

/**
 * Get error message for single deletion
 * @param person - The person that failed to delete
 * @returns string - Error message
 */
export function getDeleteErrorMessage(person: Person): string {
  const personName = getPersonName(person);
  return DELETE_CONSTANTS.MESSAGES.ERROR_SINGLE(personName);
}

/**
 * Get error message for bulk deletion
 * @returns string - Error message
 */
export function getBulkDeleteErrorMessage(): string {
  return DELETE_CONSTANTS.MESSAGES.ERROR_MULTIPLE;
}

/**
 * Create snackbar configuration for success
 * @returns object - Snackbar configuration
 */
export function getSuccessSnackbarConfig() {
  return {
    duration: DELETE_CONSTANTS.SNACKBAR.SUCCESS_DURATION,
    panelClass: DELETE_CONSTANTS.SNACKBAR.CLASSES.success,
    horizontalPosition: DELETE_CONSTANTS.SNACKBAR.POSITION.horizontal,
    verticalPosition: DELETE_CONSTANTS.SNACKBAR.POSITION.vertical
  };
}

/**
 * Create snackbar configuration for error
 * @returns object - Snackbar configuration
 */
export function getErrorSnackbarConfig() {
  return {
    duration: DELETE_CONSTANTS.SNACKBAR.ERROR_DURATION,
    panelClass: DELETE_CONSTANTS.SNACKBAR.CLASSES.error,
    horizontalPosition: DELETE_CONSTANTS.SNACKBAR.POSITION.horizontal,
    verticalPosition: DELETE_CONSTANTS.SNACKBAR.POSITION.vertical
  };
}

/**
 * Log delete operation start
 * @param person - Person being deleted
 */
export function logDeleteStart(person: Person): void {
  console.log('DeleteUtils: Starting delete operation for:', getPersonName(person), person);
}

/**
 * Log bulk delete operation start
 * @param people - People being deleted
 */
export function logBulkDeleteStart(people: Person[]): void {
  const validation = canDeleteMultiplePeople(people);
  console.log(`DeleteUtils: Starting bulk delete operation for ${validation.validCount} people:`, validation.validPeople);
}

/**
 * Log delete operation success
 * @param person - Person that was deleted
 */
export function logDeleteSuccess(person: Person): void {
  console.log('DeleteUtils: Delete operation completed successfully for:', getPersonName(person));
}

/**
 * Log bulk delete operation success
 * @param count - Number of people deleted
 */
export function logBulkDeleteSuccess(count: number): void {
  console.log(`DeleteUtils: Bulk delete operation completed successfully for ${count} people`);
}

/**
 * Log delete operation error
 * @param person - Person that failed to delete
 * @param error - The error that occurred
 */
export function logDeleteError(person: Person, error: any): void {
  console.error('DeleteUtils: Delete operation failed for:', getPersonName(person), error);
}

/**
 * Log bulk delete operation error
 * @param error - The error that occurred
 */
export function logBulkDeleteError(error: any): void {
  console.error('DeleteUtils: Bulk delete operation failed:', error);
}
