/* You can add global styles to this file, and also import other style files */
@import '~@angular/material/prebuilt-themes/indigo-pink.css';

html, body {
  height: 100%;
}

body {
  margin: 0;
  font-family: Roboto, "Helvetica Neue", sans-serif;
  background-color: #f5f5f5;
}

* {
  box-sizing: border-box;
}

/* Custom Snackbar Styles */
.success-snackbar {
  background-color: #4caf50 !important;
  color: white !important;
}

.error-snackbar {
  background-color: #f44336 !important;
  color: white !important;
}

/* Dialog Styles - Perfect Center Positioning */
.cdk-global-overlay-wrapper {
  display: flex !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  height: 100vh !important;
  width: 100vw !important;
  justify-content: center !important;
  align-items: center !important;
  z-index: 1000 !important;
  pointer-events: none !important;
}

.cdk-overlay-pane {
  position: static !important;
  pointer-events: auto !important;
}

.delete-dialog .mat-dialog-container {
  padding: 0;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  position: relative;
  margin: 0;
  transform: none !important;
}

.delete-dialog .mat-dialog-title {
  margin: 0;
}

.delete-dialog .mat-dialog-content {
  margin: 0;
}

.delete-dialog .mat-dialog-actions {
  margin: 0;
  padding: 0;
}

/* Backdrop styling */
.cdk-overlay-backdrop {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  pointer-events: auto !important;
}

.dialog-backdrop {
  background-color: rgba(0, 0, 0, 0.6) !important;
}

.warning-snackbar {
  background-color: #ff9800 !important;
  color: white !important;
}

/* Smooth transitions for all elements */
* {
  transition: all 0.2s ease-in-out;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Material Design elevation shadows */
.mat-elevation-z1 {
  box-shadow: 0 2px 1px -1px rgba(0,0,0,.2), 0 1px 1px 0 rgba(0,0,0,.14), 0 1px 3px 0 rgba(0,0,0,.12);
}

.mat-elevation-z2 {
  box-shadow: 0 3px 1px -2px rgba(0,0,0,.2), 0 2px 2px 0 rgba(0,0,0,.14), 0 1px 5px 0 rgba(0,0,0,.12);
}

.mat-elevation-z4 {
  box-shadow: 0 2px 4px -1px rgba(0,0,0,.2), 0 4px 5px 0 rgba(0,0,0,.14), 0 1px 10px 0 rgba(0,0,0,.12);
}

/* Focus styles */
.mat-button:focus,
.mat-raised-button:focus,
.mat-icon-button:focus {
  outline: 2px solid #1976d2;
  outline-offset: 2px;
}

/* Loading animation */
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

.loading {
  animation: pulse 1.5s ease-in-out infinite;
}
