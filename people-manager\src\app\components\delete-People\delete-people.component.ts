import { Component, Input, Output, EventEmitter } from '@angular/core';
import { Person } from '../../models/person.model';
import { DeletePeopleService } from './delete-people.service';

@Component({
  selector: 'app-delete-people',
  templateUrl: './delete-people.component.html',
  styleUrls: ['./delete-people.component.css']
})
export class DeletePeopleComponent {

  @Input() person: Person | null = null;
  @Input() people: Person[] = [];
  @Input() showConfirmation: boolean = false;
  @Input() buttonText: string = 'Delete';
  @Input() buttonIcon: string = 'delete';
  @Input() buttonColor: string = 'warn';
  @Input() disabled: boolean = false;

  @Output() deleteStarted = new EventEmitter<Person | Person[]>();
  @Output() deleteCompleted = new EventEmitter<Person | Person[]>();
  @Output() deleteError = new EventEmitter<any>();

  constructor(private deletePeopleService: DeletePeopleService) { }

  /**
   * Handle single person deletion
   */
  onDeletePerson(): void {
    if (!this.person) {
      console.error('DeletePeopleComponent: No person provided for deletion');
      return;
    }

    if (!this.deletePeopleService.canDeletePerson(this.person)) {
      console.error('DeletePeopleComponent: Person cannot be deleted:', this.person);
      return;
    }

    if (this.showConfirmation) {
      const confirmMessage = this.deletePeopleService.getDeleteMessage(this.person);
      if (!confirm(confirmMessage)) {
        console.log('DeletePeopleComponent: User cancelled deletion');
        return;
      }
    }

    console.log('DeletePeopleComponent: Starting delete for person:', this.person);
    this.deleteStarted.emit(this.person);

    this.deletePeopleService.deletePerson(this.person).subscribe({
      next: (result) => {
        console.log('DeletePeopleComponent: Delete completed successfully');
        this.deleteCompleted.emit(this.person);
      },
      error: (error) => {
        console.error('DeletePeopleComponent: Delete failed:', error);
        this.deleteError.emit(error);
      }
    });
  }

  /**
   * Handle multiple people deletion
   */
  onDeleteMultiplePeople(): void {
    if (!this.people || this.people.length === 0) {
      console.error('DeletePeopleComponent: No people provided for deletion');
      return;
    }

    const validPeople = this.people.filter(person => 
      this.deletePeopleService.canDeletePerson(person)
    );

    if (validPeople.length === 0) {
      console.error('DeletePeopleComponent: No valid people to delete');
      return;
    }

    if (this.showConfirmation) {
      const confirmMessage = `Are you sure you want to delete ${validPeople.length} people?`;
      if (!confirm(confirmMessage)) {
        console.log('DeletePeopleComponent: User cancelled bulk deletion');
        return;
      }
    }

    console.log('DeletePeopleComponent: Starting bulk delete for people:', validPeople);
    this.deleteStarted.emit(validPeople);

    this.deletePeopleService.deleteMultiplePeople(validPeople).subscribe({
      next: (results) => {
        console.log('DeletePeopleComponent: Bulk delete completed successfully');
        this.deleteCompleted.emit(validPeople);
      },
      error: (error) => {
        console.error('DeletePeopleComponent: Bulk delete failed:', error);
        this.deleteError.emit(error);
      }
    });
  }

  /**
   * Get the appropriate click handler based on input
   */
  onClick(): void {
    if (this.person) {
      this.onDeletePerson();
    } else if (this.people && this.people.length > 0) {
      this.onDeleteMultiplePeople();
    } else {
      console.warn('DeletePeopleComponent: No person or people provided for deletion');
    }
  }

  /**
   * Check if the delete button should be disabled
   */
  isDisabled(): boolean {
    if (this.disabled) {
      return true;
    }

    if (this.person) {
      return !this.deletePeopleService.canDeletePerson(this.person);
    }

    if (this.people && this.people.length > 0) {
      return !this.people.some(person => 
        this.deletePeopleService.canDeletePerson(person)
      );
    }

    return true;
  }

  /**
   * Get the tooltip text for the delete button
   */
  getTooltipText(): string {
    if (this.isDisabled()) {
      return 'Cannot delete: Invalid selection';
    }

    if (this.person) {
      return `Delete ${this.person.firstName} ${this.person.lastName}`;
    }

    if (this.people && this.people.length > 0) {
      return `Delete ${this.people.length} people`;
    }

    return 'Delete';
  }
}
