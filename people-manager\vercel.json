{"version": 2, "name": "people-manager", "builds": [{"src": "package.json", "use": "@vercel/static-build", "config": {"distDir": "dist/people-manager"}}], "routes": [{"src": "/(.*\\.(js|css|ico|png|jpg|jpeg|gif|svg|woff|woff2|ttf|eot))", "headers": {"Cache-Control": "public, max-age=31536000, immutable"}}, {"src": "/(.*)", "dest": "/index.html"}], "rewrites": [{"source": "/(.*)", "destination": "/index.html"}]}