<!-- Delete People Component Template -->
<div class="delete-people-container">
  
  <!-- Single Delete Button -->
  <button 
    mat-icon-button
    [color]="buttonColor"
    [disabled]="isDisabled()"
    [matTooltip]="getTooltipText()"
    (click)="onClick()"
    class="delete-button"
    aria-label="Delete person">
    
    <mat-icon>{{ buttonIcon }}</mat-icon>
  </button>

  <!-- Alternative: Raised Button with Text -->
  <ng-container *ngIf="buttonText !== 'Delete' || people?.length > 1">
    <button 
      mat-raised-button
      [color]="buttonColor"
      [disabled]="isDisabled()"
      [matTooltip]="getTooltipText()"
      (click)="onClick()"
      class="delete-button-raised"
      aria-label="Delete people">
      
      <mat-icon>{{ buttonIcon }}</mat-icon>
      <span class="button-text">{{ buttonText }}</span>
      
      <!-- Show count for multiple deletions -->
      <span *ngIf="people?.length > 1" class="delete-count">
        ({{ people.length }})
      </span>
    </button>
  </ng-container>

</div>

<!-- Debug Information (only in development) -->
<div *ngIf="false" class="debug-info">
  <h4>Debug Information:</h4>
  <p><strong>Person:</strong> {{ person | json }}</p>
  <p><strong>People Count:</strong> {{ people?.length || 0 }}</p>
  <p><strong>Show Confirmation:</strong> {{ showConfirmation }}</p>
  <p><strong>Disabled:</strong> {{ isDisabled() }}</p>
  <p><strong>Can Delete:</strong> {{ person ? deletePeopleService.canDeletePerson(person) : 'N/A' }}</p>
</div>
