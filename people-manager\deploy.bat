@echo off
echo ========================================
echo   People Manager - Vercel Deployment
echo ========================================
echo.

echo Building production version...
call npm run build

if %errorlevel% neq 0 (
    echo Build failed! Please check for errors.
    pause
    exit /b 1
)

echo.
echo Build completed successfully!
echo.
echo Ready for Vercel deployment!
echo.
echo Next steps:
echo 1. Install Vercel CLI: npm install -g vercel
echo 2. Run: vercel
echo 3. Follow the prompts
echo.
echo Or upload the 'dist/people-manager' folder to Vercel dashboard
echo.
pause
