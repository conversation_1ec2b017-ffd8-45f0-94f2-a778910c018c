/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
/** Name of the Material version that is shipped together with the schematics. */
export declare const materialVersion: string | null;
/**
 * Range of Angular versions that can be used together with the Angular Material version
 * that provides these schematics.
 */
export declare const requiredAngularVersionRange = "^8.0.0 || ^9.0.0-0";
/** HammerJS version that should be installed if gestures will be set up. */
export declare const hammerjsVersion = "^2.0.8";
