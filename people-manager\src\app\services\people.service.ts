import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError, BehaviorSubject } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { Person, PersonCreateRequest, PersonUpdateRequest } from '../models/person.model';

@Injectable({
  providedIn: 'root'
})
export class PeopleService {
  private readonly baseUrl = 'https://jsonplaceholder.typicode.com/users'; // Mock API
  private peopleSubject = new BehaviorSubject<Person[]>([]);
  public people$ = this.peopleSubject.asObservable();

  constructor(private http: HttpClient) {
    this.loadPeople();
  }

  // Get all people
  getPeople(): Observable<Person[]> {
    return this.http.get<Person[]>(this.baseUrl).pipe(
      tap(people => {
        // Transform the data to match our Person interface
        const transformedPeople = people.map(person => ({
          id: person.id,
          firstName: person.name?.split(' ')[0] || '',
          lastName: person.name?.split(' ').slice(1).join(' ') || '',
          email: person.email,
          phone: person.phone,
          address: `${person.address?.street}, ${person.address?.city}`,
          dateOfBirth: '',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }));
        this.peopleSubject.next(transformedPeople);
      }),
      catchError(this.handleError)
    );
  }

  // Get person by ID
  getPerson(id: number): Observable<Person> {
    return this.http.get<any>(`${this.baseUrl}/${id}`).pipe(
      tap(person => {
        // Transform single person data
        const transformedPerson: Person = {
          id: person.id,
          firstName: person.name?.split(' ')[0] || '',
          lastName: person.name?.split(' ').slice(1).join(' ') || '',
          email: person.email,
          phone: person.phone,
          address: `${person.address?.street}, ${person.address?.city}`,
          dateOfBirth: '',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        return transformedPerson;
      }),
      catchError(this.handleError)
    );
  }

  // Create new person
  createPerson(person: PersonCreateRequest): Observable<Person> {
    const newPerson = {
      name: `${person.firstName} ${person.lastName}`,
      email: person.email,
      phone: person.phone || '',
      address: {
        street: person.address || '',
        city: ''
      }
    };

    return this.http.post<any>(this.baseUrl, newPerson).pipe(
      tap(createdPerson => {
        const transformedPerson: Person = {
          id: createdPerson.id || Date.now(), // Use timestamp as ID for mock
          firstName: person.firstName,
          lastName: person.lastName,
          email: person.email,
          phone: person.phone,
          address: person.address,
          dateOfBirth: person.dateOfBirth,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        
        // Update local state
        const currentPeople = this.peopleSubject.value;
        this.peopleSubject.next([...currentPeople, transformedPerson]);
      }),
      catchError(this.handleError)
    );
  }

  // Update person
  updatePerson(id: number, person: PersonUpdateRequest): Observable<Person> {
    const updateData = {
      name: person.firstName && person.lastName ? `${person.firstName} ${person.lastName}` : undefined,
      email: person.email,
      phone: person.phone,
      address: person.address ? { street: person.address, city: '' } : undefined
    };

    return this.http.put<any>(`${this.baseUrl}/${id}`, updateData).pipe(
      tap(updatedPerson => {
        // Update local state
        const currentPeople = this.peopleSubject.value;
        const index = currentPeople.findIndex(p => p.id === id);
        if (index !== -1) {
          const updated: Person = {
            ...currentPeople[index],
            firstName: person.firstName || currentPeople[index].firstName,
            lastName: person.lastName || currentPeople[index].lastName,
            email: person.email || currentPeople[index].email,
            phone: person.phone || currentPeople[index].phone,
            address: person.address || currentPeople[index].address,
            dateOfBirth: person.dateOfBirth || currentPeople[index].dateOfBirth,
            updatedAt: new Date().toISOString()
          };
          currentPeople[index] = updated;
          this.peopleSubject.next([...currentPeople]);
        }
      }),
      catchError(this.handleError)
    );
  }

  // Delete person
  deletePerson(id: number): Observable<any> {
    return this.http.delete(`${this.baseUrl}/${id}`).pipe(
      tap(() => {
        // Update local state
        const currentPeople = this.peopleSubject.value;
        const filteredPeople = currentPeople.filter(p => p.id !== id);
        this.peopleSubject.next(filteredPeople);
      }),
      catchError(this.handleError)
    );
  }

  private loadPeople(): void {
    this.getPeople().subscribe();
  }

  private handleError(error: HttpErrorResponse) {
    let errorMessage = 'An unknown error occurred!';
    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = `Error: ${error.error.message}`;
    } else {
      // Server-side error
      errorMessage = `Error Code: ${error.status}\nMessage: ${error.message}`;
    }
    console.error(errorMessage);
    return throwError(errorMessage);
  }
}
