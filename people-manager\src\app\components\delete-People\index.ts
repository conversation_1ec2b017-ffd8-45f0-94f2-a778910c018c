/**
 * Delete People Module Exports
 * 
 * This file exports all the delete-related functionality for easy importing
 * throughout the application.
 */

// Component
export { DeletePeopleComponent } from './delete-people.component';

// Service
export { DeletePeopleService } from './delete-people.service';

// Utilities and Constants
export * from './delete-people.utils';

// Re-export for convenience
export {
  DELETE_CONSTANTS,
  canDeletePerson,
  canDeleteMultiplePeople,
  getPersonName,
  getDeleteConfirmationMessage,
  getBulkDeleteConfirmationMessage,
  getDeleteSuccessMessage,
  getBulkDeleteSuccessMessage,
  getDeleteErrorMessage,
  getBulkDeleteErrorMessage,
  getSuccessSnackbarConfig,
  getErrorSnackbarConfig,
  logDeleteStart,
  logBulkDeleteStart,
  logDeleteSuccess,
  logBulkDeleteSuccess,
  logDeleteError,
  logBulkDeleteError
} from './delete-people.utils';
