import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { Router } from '@angular/router';
import { MatSnackBar } from '@angular/material/snack-bar';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { Person } from '../../models/person.model';
import { PeopleService } from '../../services/people.service';

@Component({
  selector: 'app-people-list',
  templateUrl: './people-list.component.html',
  styleUrls: ['./people-list.component.css']
})
export class PeopleListComponent implements OnInit, OnDestroy {
  people: Person[] = [];
  displayedColumns: string[] = ['avatar', 'name', 'contact', 'actions'];
  loading = true;
  private destroy$ = new Subject<void>();

  constructor(
    private peopleService: PeopleService,
    private router: Router,
    private snackBar: MatSnackBar
  ) { }

  ngOnInit() {
    this.loadPeople();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadPeople() {
    this.loading = true;

    // Subscribe to the people observable for real-time updates
    this.peopleService.people$
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (people) => {
          this.people = people;
          this.loading = false;
        },
        error: (error) => {
          console.error('Error loading people:', error);
          this.snackBar.open('Error loading people', 'Close', { duration: 3000 });
          this.loading = false;
        }
      });

    // Trigger initial load from API
    this.peopleService.getPeople().subscribe({
      error: (error) => {
        console.error('Error fetching people from API:', error);
        this.snackBar.open('Error loading people from server', 'Close', { duration: 3000 });
        this.loading = false;
      }
    });
  }

  addPerson() {
    this.router.navigate(['/add']);
  }

  editPerson(person: Person) {
    this.router.navigate(['/edit', person.id]);
  }

  deletePerson(person: Person) {
    if (person.id) {
      this.peopleService.deletePerson(person.id).subscribe({
        next: () => {
          this.snackBar.open(
            `${person.firstName} ${person.lastName} has been deleted successfully`,
            'Close',
            {
              duration: 3000,
              panelClass: ['success-snackbar']
            }
          );
        },
        error: (error) => {
          console.error('Error deleting person:', error);
          this.snackBar.open(
            'Failed to delete person. Please try again.',
            'Close',
            {
              duration: 3000,
              panelClass: ['error-snackbar']
            }
          );
        }
      });
    }
  }

  getInitials(firstName: string, lastName: string): string {
    const first = firstName ? firstName.charAt(0).toUpperCase() : '';
    const last = lastName ? lastName.charAt(0).toUpperCase() : '';
    return first + last;
  }
}
