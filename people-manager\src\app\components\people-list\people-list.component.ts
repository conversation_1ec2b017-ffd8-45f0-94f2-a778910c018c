import { Component, OnInit, OnDestroy } from '@angular/core';
import { Router } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { Person } from '../../models/person.model';
import { PeopleService } from '../../services/people.service';
import { DeleteConfirmationDialogComponent } from '../delete-confirmation-dialog/delete-confirmation-dialog.component';

@Component({
  selector: 'app-people-list',
  templateUrl: './people-list.component.html',
  styleUrls: ['./people-list.component.css']
})
export class PeopleListComponent implements OnInit, OnDestroy {
  people: Person[] = [];
  displayedColumns: string[] = ['firstName', 'lastName', 'email', 'phone', 'actions'];
  loading = true;
  private destroy$ = new Subject<void>();

  constructor(
    private peopleService: PeopleService,
    private router: Router,
    private dialog: MatDialog,
    private snackBar: MatSnackBar
  ) { }

  ngOnInit() {
    this.loadPeople();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadPeople() {
    this.loading = true;
    this.peopleService.people$
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (people) => {
          this.people = people;
          this.loading = false;
        },
        error: (error) => {
          console.error('Error loading people:', error);
          this.snackBar.open('Error loading people', 'Close', { duration: 3000 });
          this.loading = false;
        }
      });
  }

  addPerson() {
    this.router.navigate(['/add']);
  }

  editPerson(person: Person) {
    this.router.navigate(['/edit', person.id]);
  }

  deletePerson(person: Person) {
    const dialogRef = this.dialog.open(DeleteConfirmationDialogComponent, {
      width: '400px',
      data: {
        title: 'Delete Person',
        message: `Are you sure you want to delete ${person.firstName} ${person.lastName}?`,
        person: person
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result && person.id) {
        this.peopleService.deletePerson(person.id).subscribe({
          next: () => {
            this.snackBar.open('Person deleted successfully', 'Close', { duration: 3000 });
          },
          error: (error) => {
            console.error('Error deleting person:', error);
            this.snackBar.open('Error deleting person', 'Close', { duration: 3000 });
          }
        });
      }
    });
  }
}
