import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { Router } from '@angular/router';
import { MatSnackBar } from '@angular/material/snack-bar';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { Person } from '../../models/person.model';
import { PeopleService } from '../../services/people.service';
import { DeletePeopleService } from '../delete-People/delete-people.service';

@Component({
  selector: 'app-people-list',
  templateUrl: './people-list.component.html',
  styleUrls: ['./people-list.component.css']
})
export class PeopleListComponent implements OnInit, OnDestroy {
  people: Person[] = [];
  displayedColumns: string[] = ['avatar', 'name', 'contact', 'actions'];
  loading = true;
  private destroy$ = new Subject<void>();

  constructor(
    private peopleService: PeopleService,
    private router: Router,
    private snackBar: MatSnackBar,
    private deletePeopleService: DeletePeopleService
  ) { }

  ngOnInit() {
    this.loadPeople();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadPeople() {
    this.loading = true;

    // Subscribe to the people observable for real-time updates
    this.peopleService.people$
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (people) => {
          console.log('PeopleListComponent: Received updated people list:', people);
          this.people = people;
          this.loading = false;
        },
        error: (error) => {
          console.error('Error loading people:', error);
          this.snackBar.open('Error loading people', 'Close', { duration: 3000 });
          this.loading = false;
        }
      });
  }

  addPerson() {
    this.router.navigate(['/add']);
  }

  editPerson(person: Person) {
    this.router.navigate(['/edit', person.id]);
  }

  deletePerson(person: Person) {
    console.log('PeopleListComponent: Delete button clicked for person:', person);

    // Use the dedicated delete service
    this.deletePeopleService.deletePerson(person).subscribe({
      next: () => {
        console.log('PeopleListComponent: Person deleted successfully via DeletePeopleService');
        // Success message is handled by the DeletePeopleService
      },
      error: (error) => {
        console.error('PeopleListComponent: Error deleting person via DeletePeopleService:', error);
        // Error message is handled by the DeletePeopleService
      }
    });
  }

  getInitials(firstName: string, lastName: string): string {
    const first = firstName ? firstName.charAt(0).toUpperCase() : '';
    const last = lastName ? lastName.charAt(0).toUpperCase() : '';
    return first + last;
  }
}
