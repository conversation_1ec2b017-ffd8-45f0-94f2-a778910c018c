/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { AnimationTriggerMetadata } from '@angular/animations';
/**
 * Animations used by the Material tabs.
 * @docs-private
 */
export declare const matTabsAnimations: {
    readonly translateTab: AnimationTriggerMetadata;
};
