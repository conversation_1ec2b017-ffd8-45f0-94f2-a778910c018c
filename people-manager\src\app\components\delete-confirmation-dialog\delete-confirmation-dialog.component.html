<div class="dialog-container">
  <div class="dialog-header">
    <div class="header-icon">
      <mat-icon class="warning-icon">delete_forever</mat-icon>
    </div>
    <h2 mat-dialog-title class="dialog-title">{{ data.title }}</h2>
  </div>

  <mat-dialog-content class="dialog-content">
    <p class="warning-message">{{ data.message }}</p>

    <div class="person-card">
      <div class="person-avatar">
        {{ getInitials(data.person.firstName, data.person.lastName) }}
      </div>
      <div class="person-details">
        <div class="person-name">
          {{ data.person.firstName }} {{ data.person.lastName }}
        </div>
        <div class="person-email">
          <mat-icon class="detail-icon">email</mat-icon>
          {{ data.person.email }}
        </div>
        <div class="person-phone" *ngIf="data.person.phone">
          <mat-icon class="detail-icon">phone</mat-icon>
          {{ data.person.phone }}
        </div>
        <div class="person-address" *ngIf="data.person.address">
          <mat-icon class="detail-icon">location_on</mat-icon>
          {{ data.person.address }}
        </div>
      </div>
    </div>

    <div class="warning-note">
      <mat-icon class="note-icon">info</mat-icon>
      <span>This action cannot be undone. All data associated with this person will be permanently removed.</span>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions class="dialog-actions">
    <button mat-button (click)="onCancel()" class="cancel-button">
      <mat-icon>close</mat-icon>
      Cancel
    </button>
    <button mat-raised-button color="warn" (click)="onConfirm()" class="delete-button">
      <mat-icon>delete</mat-icon>
      Delete Person
    </button>
  </mat-dialog-actions>
</div>
