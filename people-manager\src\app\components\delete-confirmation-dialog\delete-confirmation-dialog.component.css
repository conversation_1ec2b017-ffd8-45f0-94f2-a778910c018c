.dialog-container {
  min-width: 420px;
  max-width: 500px;
}

/* Header */
.dialog-header {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 24px 24px 16px 24px;
  border-bottom: 1px solid #e0e0e0;
}

.header-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: #ffebee;
}

.warning-icon {
  font-size: 24px;
  width: 24px;
  height: 24px;
  color: #d32f2f;
}

.dialog-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 500;
  color: #333;
}

/* Content */
.dialog-content {
  padding: 24px !important;
}

.warning-message {
  margin: 0 0 24px 0;
  color: #666;
  font-size: 1rem;
  line-height: 1.5;
}

/* Person Card */
.person-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #e9ecef;
  margin-bottom: 20px;
}

.person-avatar {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background: linear-gradient(135deg, #1976d2, #42a5f5);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1.1rem;
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(25, 118, 210, 0.3);
}

.person-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.person-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.person-email,
.person-phone,
.person-address {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 0.9rem;
  margin-bottom: 4px;
}

.detail-label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 600;
  color: #666;
  min-width: 80px;
  font-size: 0.85rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.detail-value {
  color: #333;
  flex: 1;
  font-size: 0.9rem;
}

.detail-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
  color: #999;
}

/* Warning Note */
.warning-note {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background-color: #fff3e0;
  border-radius: 8px;
  border-left: 4px solid #ff9800;
}

.note-icon {
  font-size: 20px;
  width: 20px;
  height: 20px;
  color: #ff9800;
  margin-top: 2px;
  flex-shrink: 0;
}

.warning-note span {
  font-size: 0.875rem;
  color: #e65100;
  line-height: 1.4;
}

/* Actions */
.dialog-actions {
  padding: 16px 24px 24px 24px !important;
  gap: 12px;
  border-top: 1px solid #e0e0e0;
  background-color: #fafafa;
}

.cancel-button {
  color: #666;
  border: 1px solid #ddd;
  background-color: white;
  transition: all 0.2s ease;
}

.cancel-button:hover {
  background-color: #f5f5f5;
  border-color: #bbb;
}

.delete-button {
  background-color: #d32f2f;
  color: white;
  box-shadow: 0 2px 8px rgba(211, 47, 47, 0.3);
  transition: all 0.2s ease;
}

.delete-button:hover {
  background-color: #b71c1c;
  box-shadow: 0 4px 12px rgba(211, 47, 47, 0.4);
  transform: translateY(-1px);
}

.cancel-button mat-icon,
.delete-button mat-icon {
  font-size: 18px;
  width: 18px;
  height: 18px;
  margin-right: 8px;
}

/* Responsive */
@media (max-width: 480px) {
  .dialog-container {
    min-width: 300px;
    max-width: 350px;
  }

  .dialog-header {
    padding: 20px 20px 12px 20px;
  }

  .dialog-content {
    padding: 20px !important;
  }

  .person-card {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .person-details {
    align-items: center;
  }

  .person-email,
  .person-phone,
  .person-address {
    justify-content: center;
  }

  .dialog-actions {
    flex-direction: column-reverse;
    gap: 8px;
  }

  .cancel-button,
  .delete-button {
    width: 100%;
  }
}