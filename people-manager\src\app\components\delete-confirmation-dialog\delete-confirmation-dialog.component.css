.dialog-container {
  min-width: 350px;
}

.dialog-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 16px 0;
}

.warning-icon {
  font-size: 48px;
  width: 48px;
  height: 48px;
  color: #ff9800;
  margin-bottom: 16px;
}

.dialog-content p {
  margin-bottom: 16px;
  color: #666;
}

.person-info {
  background-color: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  border-left: 4px solid #ff9800;
}

.person-info strong {
  color: #333;
}

.email {
  color: #666;
  font-size: 14px;
}

mat-dialog-actions {
  padding: 16px 24px;
}

mat-dialog-actions button {
  margin-left: 8px;
}