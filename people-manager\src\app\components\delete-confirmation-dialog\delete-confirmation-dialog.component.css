.dialog-container {
  padding: 0;
  max-width: 400px;
}

.dialog-title {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0;
  padding: 24px 24px 16px 24px;
  font-size: 20px;
  font-weight: 500;
  color: #d32f2f;
  border-bottom: 1px solid #e0e0e0;
}

.warning-icon {
  color: #ff9800;
  font-size: 24px;
  width: 24px;
  height: 24px;
}

.dialog-content {
  padding: 20px 24px;
  margin: 0;
}

.confirmation-message {
  font-size: 16px;
  margin: 0 0 12px 0;
  color: #333;
  line-height: 1.5;
}

.confirmation-message strong {
  color: #1976d2;
  font-weight: 600;
}

.warning-text {
  font-size: 14px;
  color: #666;
  margin: 0;
  font-style: italic;
}

.dialog-actions {
  padding: 16px 24px 24px 24px;
  margin: 0;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  border-top: 1px solid #e0e0e0;
}

.cancel-btn {
  color: #666;
  border: 1px solid #ddd;
  transition: all 0.3s ease;
}

.cancel-btn:hover {
  background-color: #f5f5f5;
  border-color: #bbb;
}

.delete-btn {
  background-color: #d32f2f;
  color: white;
  box-shadow: 0 2px 8px rgba(211, 47, 47, 0.3);
  transition: all 0.3s ease;
}

.delete-btn:hover {
  background-color: #b71c1c;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(211, 47, 47, 0.4);
}

.cancel-btn mat-icon,
.delete-btn mat-icon {
  margin-right: 8px;
  font-size: 18px;
  width: 18px;
  height: 18px;
}

/* Responsive design */
@media (max-width: 480px) {
  .dialog-container {
    max-width: 90vw;
  }
  
  .dialog-title {
    font-size: 18px;
    padding: 20px 20px 12px 20px;
  }
  
  .dialog-content {
    padding: 16px 20px;
  }
  
  .dialog-actions {
    padding: 12px 20px 20px 20px;
    flex-direction: column-reverse;
    gap: 8px;
  }
  
  .cancel-btn,
  .delete-btn {
    width: 100%;
    justify-content: center;
  }
}
