/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
export { MatTabsModule } from './tabs-module';
export * from './tab-group';
export { MatInkBar, _MatInkBarPositioner, _MAT_INK_BAR_POSITIONER } from './ink-bar';
export { MatTabBody, _MatTabBodyBase, MatTabBodyOriginState, MatTabBodyPositionState, MatTabBodyPortal } from './tab-body';
export { MatTabHeader, _MatTabHeaderBase } from './tab-header';
export { MatTabLabelWrapper } from './tab-label-wrapper';
export { MatTab } from './tab';
export { MatTabLabel } from './tab-label';
export { MatTabNav, MatTabLink, _MatTabNavBase, _MatTabLinkBase } from './tab-nav-bar/index';
export { MatTabContent } from './tab-content';
export { ScrollDirection } from './paginated-tab-header';
export * from './tabs-animations';
