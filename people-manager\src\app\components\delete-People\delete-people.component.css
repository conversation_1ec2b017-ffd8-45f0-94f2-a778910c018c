/* Delete People Component Styles */

.delete-people-container {
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

/* Icon <PERSON> Styles */
.delete-button {
  transition: all 0.3s ease;
  border-radius: 50%;
}

.delete-button:hover:not(:disabled) {
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(244, 67, 54, 0.3);
}

.delete-button:active:not(:disabled) {
  transform: scale(0.95);
}

.delete-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Raised <PERSON><PERSON> Styles */
.delete-button-raised {
  transition: all 0.3s ease;
  border-radius: 8px;
  min-width: 120px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.delete-button-raised:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(244, 67, 54, 0.3);
}

.delete-button-raised:active:not(:disabled) {
  transform: translateY(0);
}

.delete-button-raised:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Button Text */
.button-text {
  font-weight: 500;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Delete Count Badge */
.delete-count {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 2px 8px;
  font-size: 12px;
  font-weight: bold;
  margin-left: 4px;
}

/* Icon Styles */
.delete-button mat-icon,
.delete-button-raised mat-icon {
  font-size: 20px;
  width: 20px;
  height: 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .delete-button-raised {
    min-width: 100px;
    height: 36px;
  }
  
  .button-text {
    font-size: 12px;
  }
  
  .delete-button mat-icon,
  .delete-button-raised mat-icon {
    font-size: 18px;
    width: 18px;
    height: 18px;
  }
}

/* Color Variants */
.delete-button.mat-warn,
.delete-button-raised.mat-warn {
  background-color: #f44336;
  color: white;
}

.delete-button.mat-warn:hover:not(:disabled) {
  background-color: #d32f2f;
}

.delete-button-raised.mat-warn:hover:not(:disabled) {
  background-color: #d32f2f;
}

/* Focus Styles for Accessibility */
.delete-button:focus,
.delete-button-raised:focus {
  outline: 2px solid #f44336;
  outline-offset: 2px;
}

/* Loading State (if needed) */
.delete-button.loading,
.delete-button-raised.loading {
  pointer-events: none;
  opacity: 0.7;
}

.delete-button.loading mat-icon,
.delete-button-raised.loading mat-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Debug Information Styles */
.debug-info {
  margin-top: 16px;
  padding: 12px;
  background-color: #f5f5f5;
  border-radius: 4px;
  border-left: 4px solid #2196f3;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.debug-info h4 {
  margin: 0 0 8px 0;
  color: #2196f3;
  font-size: 14px;
}

.debug-info p {
  margin: 4px 0;
  color: #666;
}

/* Animation for successful deletion */
@keyframes deleteSuccess {
  0% { transform: scale(1); }
  50% { transform: scale(1.2); color: #4caf50; }
  100% { transform: scale(1); }
}

.delete-button.success,
.delete-button-raised.success {
  animation: deleteSuccess 0.6s ease;
}

/* Tooltip Customization */
.mat-tooltip {
  font-size: 12px;
  background-color: rgba(0, 0, 0, 0.9);
  color: white;
  border-radius: 4px;
  padding: 6px 8px;
}
