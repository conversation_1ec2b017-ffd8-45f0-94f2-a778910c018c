import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { MatSnackBar } from '@angular/material/snack-bar';
import { PersonCreateRequest, PersonUpdateRequest } from '../../models/person.model';
import { PeopleService } from '../../services/people.service';

@Component({
  selector: 'app-person-edit',
  templateUrl: './person-edit.component.html',
  styleUrls: ['./person-edit.component.css']
})
export class PersonEditComponent implements OnInit {
  personForm: FormGroup;
  isEditMode = false;
  personId: number | null = null;
  loading = false;
  submitting = false;

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private peopleService: PeopleService,
    private snackBar: MatSnackBar
  ) {
    this.personForm = this.createForm();
  }

  ngOnInit() {
    this.route.params.subscribe(params => {
      if (params['id']) {
        this.isEditMode = true;
        this.personId = +params['id'];
        this.loadPerson();
      }
    });
  }

  createForm(): FormGroup {
    return this.fb.group({
      firstName: ['', [Validators.required, Validators.minLength(2)]],
      lastName: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      phone: [''],
      address: [''],
      dateOfBirth: ['']
    });
  }

  loadPerson() {
    if (!this.personId) return;

    this.loading = true;
    this.peopleService.getPerson(this.personId).subscribe({
      next: (person) => {
        this.personForm.patchValue({
          firstName: person.firstName,
          lastName: person.lastName,
          email: person.email,
          phone: person.phone || '',
          address: person.address || '',
          dateOfBirth: person.dateOfBirth || ''
        });
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading person:', error);
        this.snackBar.open('Error loading person data', 'Close', { duration: 3000 });
        this.loading = false;
        this.router.navigate(['/']);
      }
    });
  }

  onSubmit() {
    if (this.personForm.invalid) {
      this.markFormGroupTouched();
      return;
    }

    this.submitting = true;
    const formValue = this.personForm.value;

    if (this.isEditMode && this.personId) {
      // Update existing person
      const updateRequest: PersonUpdateRequest = {
        firstName: formValue.firstName,
        lastName: formValue.lastName,
        email: formValue.email,
        phone: formValue.phone,
        address: formValue.address,
        dateOfBirth: formValue.dateOfBirth
      };

      this.peopleService.updatePerson(this.personId, updateRequest).subscribe({
        next: () => {
          this.snackBar.open('Person updated successfully', 'Close', { duration: 3000 });
          this.router.navigate(['/']);
        },
        error: (error) => {
          console.error('Error updating person:', error);
          this.snackBar.open('Error updating person', 'Close', { duration: 3000 });
          this.submitting = false;
        }
      });
    } else {
      // Create new person
      const createRequest: PersonCreateRequest = {
        firstName: formValue.firstName,
        lastName: formValue.lastName,
        email: formValue.email,
        phone: formValue.phone,
        address: formValue.address,
        dateOfBirth: formValue.dateOfBirth
      };

      this.peopleService.createPerson(createRequest).subscribe({
        next: () => {
          this.snackBar.open('Person created successfully', 'Close', { duration: 3000 });
          this.router.navigate(['/']);
        },
        error: (error) => {
          console.error('Error creating person:', error);
          this.snackBar.open('Error creating person', 'Close', { duration: 3000 });
          this.submitting = false;
        }
      });
    }
  }

  onCancel() {
    this.router.navigate(['/']);
  }

  private markFormGroupTouched() {
    Object.keys(this.personForm.controls).forEach(key => {
      const control = this.personForm.get(key);
      if (control) {
        control.markAsTouched();
      }
    });
  }

  getErrorMessage(fieldName: string): string {
    const control = this.personForm.get(fieldName);
    if (control?.hasError('required')) {
      return `${fieldName} is required`;
    }
    if (control?.hasError('email')) {
      return 'Please enter a valid email';
    }
    if (control?.hasError('minlength')) {
      return `${fieldName} must be at least ${control.errors?.['minlength']?.requiredLength} characters`;
    }
    return '';
  }
}
