<div class="person-edit-container">
  <mat-card>
    <mat-card-header>
      <mat-card-title>{{ isEditMode ? 'Edit Person' : 'Add New Person' }}</mat-card-title>
      <mat-card-subtitle>{{ isEditMode ? 'Update person information' : 'Enter person details' }}</mat-card-subtitle>
    </mat-card-header>

    <mat-card-content>
      <div *ngIf="loading" class="loading-container">
        <mat-spinner></mat-spinner>
        <p>Loading person data...</p>
      </div>

      <form [formGroup]="personForm" (ngSubmit)="onSubmit()" *ngIf="!loading">
        <div class="form-row">
          <mat-form-field appearance="outline" class="form-field">
            <mat-label>First Name</mat-label>
            <input matInput formControlName="firstName" placeholder="Enter first name">
            <mat-error *ngIf="personForm.get('firstName')?.touched && personForm.get('firstName')?.invalid">
              {{ getErrorMessage('firstName') }}
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="form-field">
            <mat-label>Last Name</mat-label>
            <input matInput formControlName="lastName" placeholder="Enter last name">
            <mat-error *ngIf="personForm.get('lastName')?.touched && personForm.get('lastName')?.invalid">
              {{ getErrorMessage('lastName') }}
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline" class="form-field full-width">
            <mat-label>Email</mat-label>
            <input matInput type="email" formControlName="email" placeholder="Enter email address">
            <mat-error *ngIf="personForm.get('email')?.touched && personForm.get('email')?.invalid">
              {{ getErrorMessage('email') }}
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline" class="form-field">
            <mat-label>Phone</mat-label>
            <input matInput formControlName="phone" placeholder="Enter phone number">
          </mat-form-field>

          <mat-form-field appearance="outline" class="form-field">
            <mat-label>Date of Birth</mat-label>
            <input matInput type="date" formControlName="dateOfBirth">
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline" class="form-field full-width">
            <mat-label>Address</mat-label>
            <textarea matInput formControlName="address" placeholder="Enter address" rows="3"></textarea>
          </mat-form-field>
        </div>

        <div class="form-actions">
          <button mat-button type="button" (click)="onCancel()" [disabled]="submitting">
            Cancel
          </button>
          <button mat-raised-button color="primary" type="submit" [disabled]="submitting">
            <mat-spinner diameter="20" *ngIf="submitting"></mat-spinner>
            <span *ngIf="!submitting">{{ isEditMode ? 'Update' : 'Create' }}</span>
            <span *ngIf="submitting">{{ isEditMode ? 'Updating...' : 'Creating...' }}</span>
          </button>
        </div>
      </form>
    </mat-card-content>
  </mat-card>
</div>
