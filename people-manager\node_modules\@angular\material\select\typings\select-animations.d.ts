/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { AnimationTriggerMetadata } from '@angular/animations';
/**
 * The following are all the animations for the mat-select component, with each
 * const containing the metadata for one animation.
 *
 * The values below match the implementation of the AngularJS Material mat-select animation.
 * @docs-private
 */
export declare const matSelectAnimations: {
    readonly transformPanelWrap: AnimationTriggerMetadata;
    readonly transformPanel: AnimationTriggerMetadata;
    readonly fadeInContent: AnimationTriggerMetadata;
};
/**
 * @deprecated
 * @breaking-change 8.0.0
 * @docs-private
 */
export declare const transformPanel: AnimationTriggerMetadata;
/**
 * @deprecated
 * @breaking-change 8.0.0
 * @docs-private
 */
export declare const fadeInContent: AnimationTriggerMetadata;
